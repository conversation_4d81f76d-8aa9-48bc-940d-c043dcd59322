// نظام معاينة وطباعة القوالب
window.TemplatePreview = {
    // إعدادات المعاينة
    config: {
        version: '1.0.0',
        defaultZoom: 100,
        minZoom: 25,
        maxZoom: 200,
        zoomStep: 25,
        defaultPageSize: 'a4',
        defaultOrientation: 'portrait'
    },

    // حالة المعاينة
    state: {
        currentTemplate: null,
        currentZoom: 100,
        pageSize: 'a4',
        orientation: 'portrait',
        variables: {},
        isLoading: false
    },

    // تهيئة نظام المعاينة
    init: function() {
        console.log('👁️ تهيئة نظام المعاينة...');
        this.setupEventListeners();
        this.loadTemplateFromURL();
        this.initializeVariables();
        this.setupPageSizeSelectors();
        this.setupOrientationSelectors();
        console.log('✅ تم تهيئة نظام المعاينة بنجاح');
    },

    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // تحديث الزوم عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.adjustPreviewSize();
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch (e.key) {
                    case 'p':
                        e.preventDefault();
                        this.printTemplate();
                        break;
                    case '+':
                    case '=':
                        e.preventDefault();
                        this.zoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        this.zoomOut();
                        break;
                }
            }
        });
    },

    // تحميل القالب من URL
    loadTemplateFromURL: function() {
        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template');
        
        if (templateId) {
            this.loadTemplate(templateId);
        } else {
            this.loadSampleTemplate();
        }
    },

    // تحميل القالب
    loadTemplate: function(templateId) {
        this.showLoading(true);
        
        try {
            // محاولة تحميل القالب من قاعدة البيانات
            const template = window.Database.getById('templates', templateId);
            
            if (template) {
                this.state.currentTemplate = template;
                this.renderTemplate(template);
            } else {
                throw new Error('القالب غير موجود');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل القالب:', error);
            this.loadSampleTemplate();
        } finally {
            this.showLoading(false);
        }
    },

    // تحميل قالب تجريبي
    loadSampleTemplate: function() {
        const sampleTemplate = {
            id: 'sample',
            name: 'فاتورة تجريبية',
            type: 'invoice',
            content: this.getSampleInvoiceContent()
        };
        
        this.state.currentTemplate = sampleTemplate;
        this.renderTemplate(sampleTemplate);
    },

    // عرض القالب
    renderTemplate: function(template) {
        const previewContent = document.getElementById('previewContent');
        
        if (!previewContent) return;

        // تطبيق محتوى القالب
        let content = template.content || this.getSampleInvoiceContent();
        
        // استبدال المتغيرات
        content = this.replaceVariables(content);
        
        previewContent.innerHTML = content;
        
        // تحديث عنوان النافذة
        document.title = `معاينة القالب - ${template.name}`;
        
        console.log('📄 تم عرض القالب:', template.name);
    },

    // الحصول على محتوى فاتورة تجريبية
    getSampleInvoiceContent: function() {
        return `
            <div class="invoice-header text-center mb-4">
                <h2 style="color: #007bff; margin-bottom: 1rem;">{{company.name}}</h2>
                <p style="color: #6c757d;">الرياض، المملكة العربية السعودية</p>
                <p style="color: #6c757d;">هاتف: +966112345678 | البريد: <EMAIL></p>
                <hr style="border-color: #007bff; border-width: 2px;">
            </div>
            
            <div class="row mb-4">
                <div class="col-6">
                    <h5 style="color: #495057;">فاتورة رقم: {{invoice.number}}</h5>
                    <p><strong>التاريخ:</strong> {{invoice.date}}</p>
                    <p><strong>تاريخ الاستحقاق:</strong> {{invoice.due_date}}</p>
                </div>
                <div class="col-6 text-end">
                    <h5 style="color: #495057;">بيانات العميل</h5>
                    <p><strong>{{customer.name}}</strong></p>
                    <p>{{customer.address}}</p>
                    <p>{{customer.phone}}</p>
                </div>
            </div>
            
            <table class="table table-bordered" style="margin-bottom: 2rem;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style="padding: 1rem; border: 1px solid #dee2e6;">الخدمة</th>
                        <th style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">الكمية</th>
                        <th style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">السعر</th>
                        <th style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 1rem; border: 1px solid #dee2e6;">تذكرة طيران - الرياض إلى القاهرة</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">1</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">1,500 ريال</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">1,500 ريال</td>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border: 1px solid #dee2e6;">حجز فندق - 3 ليالي</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">1</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">900 ريال</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">900 ريال</td>
                    </tr>
                    <tr>
                        <td style="padding: 1rem; border: 1px solid #dee2e6;">تأمين السفر</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">1</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">120 ريال</td>
                        <td style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">120 ريال</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr style="background-color: #f8f9fa;">
                        <th colspan="3" style="padding: 1rem; border: 1px solid #dee2e6; text-align: left;">المجموع الفرعي</th>
                        <th style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">2,520 ريال</th>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <th colspan="3" style="padding: 1rem; border: 1px solid #dee2e6; text-align: left;">ضريبة القيمة المضافة (15%)</th>
                        <th style="padding: 1rem; border: 1px solid #dee2e6; text-align: center;">378 ريال</th>
                    </tr>
                    <tr style="background-color: #007bff; color: white;">
                        <th colspan="3" style="padding: 1rem; border: 1px solid #007bff; text-align: left;">الإجمالي النهائي</th>
                        <th style="padding: 1rem; border: 1px solid #007bff; text-align: center;">{{invoice.total}} ريال</th>
                    </tr>
                </tfoot>
            </table>
            
            <div class="row mt-4">
                <div class="col-6">
                    <h6 style="color: #495057;">شروط الدفع:</h6>
                    <p style="font-size: 0.9rem; color: #6c757d;">
                        - الدفع مستحق خلال 30 يوم من تاريخ الفاتورة<br>
                        - يرجى ذكر رقم الفاتورة عند الدفع<br>
                        - جميع الأسعار شاملة ضريبة القيمة المضافة
                    </p>
                </div>
                <div class="col-6 text-end">
                    <h6 style="color: #495057;">معلومات البنك:</h6>
                    <p style="font-size: 0.9rem; color: #6c757d;">
                        البنك الأهلي السعودي<br>
                        رقم الحساب: 123456789<br>
                        IBAN: *********************
                    </p>
                </div>
            </div>
            
            <div class="text-center mt-4" style="border-top: 2px solid #007bff; padding-top: 1rem;">
                <p style="color: #6c757d; font-size: 0.9rem;">
                    شكراً لاختياركم قيمة الوعد للسفريات - نتطلع لخدمتكم مرة أخرى
                </p>
            </div>
        `;
    },

    // تهيئة المتغيرات
    initializeVariables: function() {
        this.state.variables = {
            'company.name': 'قيمة الوعد للسفريات',
            'invoice.number': 'INV-2024-001',
            'invoice.date': new Date().toLocaleDateString('ar-SA'),
            'invoice.due_date': new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-SA'),
            'invoice.total': '2,898',
            'customer.name': 'أحمد محمد السعيد',
            'customer.address': 'الرياض، المملكة العربية السعودية',
            'customer.phone': '+966501234567'
        };
    },

    // استبدال المتغيرات
    replaceVariables: function(content) {
        let result = content;
        
        Object.entries(this.state.variables).forEach(([key, value]) => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            result = result.replace(regex, value);
        });
        
        return result;
    },

    // تحديث متغير
    updateVariable: function(key, value) {
        this.state.variables[key] = value;
        console.log(`📝 تم تحديث المتغير ${key}:`, value);
    },

    // تطبيق البيانات
    applyData: function() {
        if (this.state.currentTemplate) {
            this.renderTemplate(this.state.currentTemplate);
            console.log('✅ تم تطبيق البيانات الجديدة');
        }
    },

    // إعداد محددات حجم الصفحة
    setupPageSizeSelectors: function() {
        document.querySelectorAll('.size-option').forEach(option => {
            option.addEventListener('click', () => {
                // إزالة التحديد من جميع الخيارات
                document.querySelectorAll('.size-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                
                // تحديد الخيار الحالي
                option.classList.add('active');
                
                // تطبيق حجم الصفحة
                const size = option.dataset.size;
                this.changePageSize(size);
            });
        });
    },

    // إعداد محددات اتجاه الصفحة
    setupOrientationSelectors: function() {
        document.querySelectorAll('.orientation-option').forEach(option => {
            option.addEventListener('click', () => {
                // إزالة التحديد من جميع الخيارات
                document.querySelectorAll('.orientation-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                
                // تحديد الخيار الحالي
                option.classList.add('active');
                
                // تطبيق اتجاه الصفحة
                const orientation = option.dataset.orientation;
                this.changeOrientation(orientation);
            });
        });
    },

    // تغيير حجم الصفحة
    changePageSize: function(size) {
        const paper = document.getElementById('previewPaper');
        if (!paper) return;

        // إزالة الفئات السابقة
        paper.classList.remove('a4', 'letter', 'legal');
        
        // إضافة الفئة الجديدة
        paper.classList.add(size);
        
        this.state.pageSize = size;
        console.log('📄 تم تغيير حجم الصفحة إلى:', size);
    },

    // تغيير اتجاه الصفحة
    changeOrientation: function(orientation) {
        const paper = document.getElementById('previewPaper');
        if (!paper) return;

        if (orientation === 'landscape') {
            paper.style.transform = `scale(${this.state.currentZoom / 100}) rotate(90deg)`;
        } else {
            paper.style.transform = `scale(${this.state.currentZoom / 100})`;
        }
        
        this.state.orientation = orientation;
        console.log('🔄 تم تغيير اتجاه الصفحة إلى:', orientation);
    },

    // تكبير العرض
    zoomIn: function() {
        if (this.state.currentZoom < this.config.maxZoom) {
            this.state.currentZoom += this.config.zoomStep;
            this.updateZoom();
        }
    },

    // تصغير العرض
    zoomOut: function() {
        if (this.state.currentZoom > this.config.minZoom) {
            this.state.currentZoom -= this.config.zoomStep;
            this.updateZoom();
        }
    },

    // تحديث الزوم
    updateZoom: function() {
        const paper = document.getElementById('previewPaper');
        const zoomLevel = document.getElementById('zoomLevel');
        
        if (paper) {
            if (this.state.orientation === 'landscape') {
                paper.style.transform = `scale(${this.state.currentZoom / 100}) rotate(90deg)`;
            } else {
                paper.style.transform = `scale(${this.state.currentZoom / 100})`;
            }
        }
        
        if (zoomLevel) {
            zoomLevel.textContent = `${this.state.currentZoom}%`;
        }
        
        console.log('🔍 تم تحديث الزوم إلى:', this.state.currentZoom + '%');
    },

    // تعديل حجم المعاينة
    adjustPreviewSize: function() {
        // سيتم تطوير هذه الوظيفة لاحقاً للتكيف مع حجم النافذة
    },

    // عرض/إخفاء شاشة التحميل
    showLoading: function(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
        this.state.isLoading = show;
    },

    // تحديث المعاينة
    refreshPreview: function() {
        if (this.state.currentTemplate) {
            this.renderTemplate(this.state.currentTemplate);
            console.log('🔄 تم تحديث المعاينة');
        }
    }
};

// وظائف الإجراءات العامة
function updateVariable(key, value) {
    window.TemplatePreview.updateVariable(key, value);
}

function applyData() {
    window.TemplatePreview.applyData();
}

function changePageSize(size) {
    window.TemplatePreview.changePageSize(size);
}

function zoomIn() {
    window.TemplatePreview.zoomIn();
}

function zoomOut() {
    window.TemplatePreview.zoomOut();
}

function refreshPreview() {
    window.TemplatePreview.refreshPreview();
}

function printTemplate() {
    console.log('🖨️ طباعة القالب...');
    window.print();
}

function exportPDF() {
    console.log('📄 تصدير PDF...');
    alert('سيتم تطوير وظيفة تصدير PDF قريباً...');
}

function exportImage() {
    console.log('🖼️ تصدير صورة...');
    alert('سيتم تطوير وظيفة تصدير الصورة قريباً...');
}

function saveAsPDF() {
    console.log('💾 حفظ كـ PDF...');
    alert('سيتم تطوير وظيفة الحفظ قريباً...');
}

function emailTemplate() {
    console.log('📧 إرسال بالبريد...');
    alert('سيتم تطوير وظيفة الإرسال بالبريد قريباً...');
}

function shareTemplate() {
    console.log('🔗 مشاركة القالب...');
    alert('سيتم تطوير وظيفة المشاركة قريباً...');
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.TemplatePreview.init();
});
