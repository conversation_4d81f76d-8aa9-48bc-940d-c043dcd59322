<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل لنظام القوالب</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Main CSS -->
    <link rel="stylesheet" href="styles/main.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .test-content {
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .test-button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }
        
        .test-button.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .status-pending {
            background: #ffc107;
        }
        
        .navbar-demo {
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial me-2"></i>اختبار التنقل لنظام القوالب</h1>
            <p>اختبار شامل لقائمة القوالب في الشريط العلوي</p>
        </div>
        
        <div class="test-content">
            <!-- عرض الشريط العلوي التجريبي -->
            <div class="test-section">
                <h4><i class="fas fa-eye me-2"></i>معاينة الشريط العلوي</h4>
                <p>هذا مثال على كيفية ظهور قائمة القوالب في الشريط العلوي:</p>
                
                <nav class="navbar navbar-expand-xl navbar-dark bg-primary navbar-demo">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="fas fa-plane me-2"></i>قيمة الوعد للسفريات
                        </a>
                        
                        <div class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-chart-bar"></i>
                                    <span class="nav-text">التقارير</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-users me-2"></i>تقارير العملاء
                                    </a></li>
                                </ul>
                            </li>
                            
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-file-alt"></i>
                                    <span class="nav-text">القوالب</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="templates_system.html">
                                        <i class="fas fa-th-large me-2"></i>إدارة القوالب
                                    </a></li>
                                    <li><a class="dropdown-item" href="template_editor.html">
                                        <i class="fas fa-edit me-2"></i>محرر القوالب
                                    </a></li>
                                    <li><a class="dropdown-item" href="template_preview.html">
                                        <i class="fas fa-eye me-2"></i>معاينة القوالب
                                    </a></li>
                                    <li><a class="dropdown-item" href="template_settings.html">
                                        <i class="fas fa-cog me-2"></i>إعدادات القوالب
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="showTemplateCategories()">
                                        <i class="fas fa-tags me-2"></i>فئات القوالب
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="showTemplateStatistics()">
                                        <i class="fas fa-chart-pie me-2"></i>إحصائيات الاستخدام
                                    </a></li>
                                </ul>
                            </li>
                            
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                    <span class="nav-text">الإعدادات</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
                                    </a></li>
                                </ul>
                            </li>
                        </div>
                    </div>
                </nav>
            </div>
            
            <!-- اختبار الروابط -->
            <div class="test-section">
                <h4><i class="fas fa-link me-2"></i>اختبار روابط نظام القوالب</h4>
                <p>انقر على الأزرار التالية لاختبار جميع صفحات نظام القوالب:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>الصفحات الرئيسية:</h6>
                        <a href="templates_system.html" class="test-button" target="_blank">
                            <i class="fas fa-th-large me-1"></i>إدارة القوالب
                            <span class="status-indicator status-success"></span>
                        </a>
                        <a href="template_editor.html" class="test-button success" target="_blank">
                            <i class="fas fa-edit me-1"></i>محرر القوالب
                            <span class="status-indicator status-success"></span>
                        </a>
                        <a href="template_preview.html" class="test-button info" target="_blank">
                            <i class="fas fa-eye me-1"></i>معاينة القوالب
                            <span class="status-indicator status-success"></span>
                        </a>
                        <a href="template_settings.html" class="test-button warning" target="_blank">
                            <i class="fas fa-cog me-1"></i>إعدادات القوالب
                            <span class="status-indicator status-success"></span>
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>الوظائف الإضافية:</h6>
                        <button class="test-button" onclick="showTemplateCategories()">
                            <i class="fas fa-tags me-1"></i>فئات القوالب
                            <span class="status-indicator status-success"></span>
                        </button>
                        <button class="test-button" onclick="showTemplateStatistics()">
                            <i class="fas fa-chart-pie me-1"></i>إحصائيات الاستخدام
                            <span class="status-indicator status-success"></span>
                        </button>
                        <a href="index.html" class="test-button success">
                            <i class="fas fa-home me-1"></i>العودة للنظام الرئيسي
                            <span class="status-indicator status-success"></span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="test-section">
                <h4><i class="fas fa-info-circle me-2"></i>معلومات النظام</h4>
                <div class="row">
                    <div class="col-md-4">
                        <h6>الملفات المطلوبة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i>templates_system.html</li>
                            <li><i class="fas fa-check text-success me-1"></i>template_editor.html</li>
                            <li><i class="fas fa-check text-success me-1"></i>template_preview.html</li>
                            <li><i class="fas fa-check text-success me-1"></i>template_settings.html</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>ملفات JavaScript:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i>js/templates-system.js</li>
                            <li><i class="fas fa-check text-success me-1"></i>js/template-editor.js</li>
                            <li><i class="fas fa-check text-success me-1"></i>js/template-preview.js</li>
                            <li><i class="fas fa-check text-success me-1"></i>js/main.js (محدث)</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>التحسينات:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i>قائمة في الشريط العلوي</li>
                            <li><i class="fas fa-check text-success me-1"></i>تصميم متجاوب</li>
                            <li><i class="fas fa-check text-success me-1"></i>أيقونات احترافية</li>
                            <li><i class="fas fa-check text-success me-1"></i>دعم RTL كامل</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- تعليمات الاستخدام -->
            <div class="test-section">
                <h4><i class="fas fa-book me-2"></i>تعليمات الاستخدام</h4>
                <ol>
                    <li>افتح الصفحة الرئيسية <code>index.html</code></li>
                    <li>ابحث عن قائمة "القوالب" في الشريط العلوي بين "التقارير" و "الإعدادات"</li>
                    <li>انقر على "القوالب" لعرض القائمة المنسدلة</li>
                    <li>اختر أي من الخيارات المتاحة:
                        <ul>
                            <li><strong>إدارة القوالب:</strong> الواجهة الرئيسية لإدارة جميع القوالب</li>
                            <li><strong>محرر القوالب:</strong> أداة تصميم القوالب بالسحب والإفلات</li>
                            <li><strong>معاينة القوالب:</strong> عرض وطباعة القوالب</li>
                            <li><strong>إعدادات القوالب:</strong> تخصيص إعدادات النظام</li>
                            <li><strong>فئات القوالب:</strong> إدارة تصنيفات القوالب</li>
                            <li><strong>إحصائيات الاستخدام:</strong> تقارير استخدام القوالب</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // اختبار الوظائف
        function showTemplateCategories() {
            alert('✅ تم استدعاء وظيفة عرض فئات القوالب بنجاح!\n\nسيتم فتح نافذة إدارة فئات القوالب.');
            console.log('📂 عرض فئات القوالب');
        }

        function showTemplateStatistics() {
            alert('✅ تم استدعاء وظيفة عرض إحصائيات القوالب بنجاح!\n\nسيتم فتح نافذة إحصائيات الاستخدام.');
            console.log('📊 عرض إحصائيات القوالب');
        }
        
        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 تم تحميل صفحة اختبار نظام القوالب بنجاح!');
            console.log('🔗 جميع الروابط والوظائف جاهزة للاختبار');
        });
    </script>
</body>
</html>
