<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التشخيص المتقدم للمبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .diagnostic-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status-card.success { border-left-color: #28a745; }
        .status-card.warning { border-left-color: #ffc107; }
        .status-card.error { border-left-color: #dc3545; }
        .status-card.info { border-left-color: #17a2b8; }
        .log-terminal {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            height: 400px;
            overflow-y: auto;
            margin: 20px;
        }
        .btn-diagnostic {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 25px;
            margin: 8px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        .btn-diagnostic:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
            color: white;
        }
        .progress-container {
            margin: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .real-time-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="header">
            <h1><i class="fas fa-stethoscope me-3"></i>نظام التشخيص المتقدم للمبيعات</h1>
            <p class="mb-0">مراقبة وتشخيص شامل لحالة نظام المبيعات <span class="real-time-indicator"></span></p>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="text-center p-3">
            <button class="btn btn-diagnostic" onclick="runFullDiagnostic()">
                <i class="fas fa-search me-2"></i>تشخيص شامل
            </button>
            <button class="btn btn-diagnostic" onclick="runQuickTest()">
                <i class="fas fa-bolt me-2"></i>اختبار سريع
            </button>
            <button class="btn btn-diagnostic" onclick="autoFix()">
                <i class="fas fa-magic me-2"></i>إصلاح تلقائي
            </button>
            <button class="btn btn-diagnostic" onclick="generateReport()">
                <i class="fas fa-file-alt me-2"></i>تقرير مفصل
            </button>
            <button class="btn btn-diagnostic" onclick="startMonitoring()">
                <i class="fas fa-eye me-2"></i>مراقبة مستمرة
            </button>
        </div>

        <!-- شريط التقدم -->
        <div class="progress-container">
            <div class="progress" style="height: 8px;">
                <div id="diagnostic-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
            </div>
            <small id="progress-text" class="text-muted">جاهز للتشخيص...</small>
        </div>

        <!-- بطاقات الحالة -->
        <div class="status-grid">
            <div class="status-card" id="component-status">
                <h5><i class="fas fa-puzzle-piece me-2"></i>حالة المكون</h5>
                <div class="metric-value" id="component-metric">--</div>
                <div class="metric-label">حالة تحميل المكون</div>
                <div id="component-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>

            <div class="status-card" id="functions-status">
                <h5><i class="fas fa-cogs me-2"></i>الوظائف</h5>
                <div class="metric-value" id="functions-metric">--</div>
                <div class="metric-label">الوظائف المتاحة</div>
                <div id="functions-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>

            <div class="status-card" id="data-status">
                <h5><i class="fas fa-database me-2"></i>البيانات</h5>
                <div class="metric-value" id="data-metric">--</div>
                <div class="metric-label">حالة البيانات</div>
                <div id="data-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>

            <div class="status-card" id="performance-status">
                <h5><i class="fas fa-tachometer-alt me-2"></i>الأداء</h5>
                <div class="metric-value" id="performance-metric">--</div>
                <div class="metric-label">سرعة الاستجابة</div>
                <div id="performance-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>

            <div class="status-card" id="errors-status">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>الأخطاء</h5>
                <div class="metric-value" id="errors-metric">--</div>
                <div class="metric-label">عدد الأخطاء</div>
                <div id="errors-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>

            <div class="status-card" id="memory-status">
                <h5><i class="fas fa-memory me-2"></i>الذاكرة</h5>
                <div class="metric-value" id="memory-metric">--</div>
                <div class="metric-label">استخدام الذاكرة</div>
                <div id="memory-details" class="mt-2 small text-muted">جاري الفحص...</div>
            </div>
        </div>

        <!-- طرفية السجل -->
        <div class="log-terminal" id="diagnostic-log">
            <div class="text-success">[SYSTEM] نظام التشخيص المتقدم للمبيعات جاهز</div>
            <div class="text-info">[INFO] انقر على "تشخيص شامل" لبدء الفحص</div>
            <div class="text-warning">[READY] جميع الأنظمة جاهزة للتشغيل</div>
        </div>

        <!-- منطقة عرض النظام -->
        <div class="p-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-desktop me-2"></i>عرض نظام المبيعات المباشر</h5>
                </div>
                <div class="card-body">
                    <div id="main-content" style="min-height: 300px;">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>سيتم عرض نظام المبيعات هنا بعد التشخيص</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // متغيرات النظام
        let diagnosticData = {
            startTime: null,
            errors: [],
            warnings: [],
            performance: {},
            monitoring: false
        };

        // وظائف السجل
        function log(message, type = 'info') {
            const terminal = document.getElementById('diagnostic-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            terminal.appendChild(logEntry);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress(percent, text) {
            document.getElementById('diagnostic-progress').style.width = percent + '%';
            document.getElementById('progress-text').textContent = text;
        }

        // تحديث بطاقة الحالة
        function updateStatusCard(cardId, status, metric, details) {
            const card = document.getElementById(cardId);
            const metricEl = document.getElementById(cardId.replace('-status', '-metric'));
            const detailsEl = document.getElementById(cardId.replace('-status', '-details'));
            
            // إزالة الفئات السابقة
            card.classList.remove('success', 'warning', 'error', 'info');
            card.classList.add(status);
            
            metricEl.textContent = metric;
            detailsEl.innerHTML = details;
        }

        // التشخيص الشامل
        async function runFullDiagnostic() {
            log('🔍 بدء التشخيص الشامل للنظام...', 'info');
            diagnosticData.startTime = Date.now();
            diagnosticData.errors = [];
            diagnosticData.warnings = [];
            
            updateProgress(0, 'بدء التشخيص...');
            
            // فحص تحميل المكون
            await checkComponent();
            updateProgress(20, 'فحص المكون...');
            
            // فحص الوظائف
            await checkFunctions();
            updateProgress(40, 'فحص الوظائف...');
            
            // فحص البيانات
            await checkData();
            updateProgress(60, 'فحص البيانات...');
            
            // فحص الأداء
            await checkPerformance();
            updateProgress(80, 'فحص الأداء...');
            
            // فحص الأخطاء والذاكرة
            await checkErrorsAndMemory();
            updateProgress(100, 'اكتمل التشخيص');
            
            log('🎉 اكتمل التشخيص الشامل', 'success');
            
            // عرض ملخص
            showDiagnosticSummary();
        }

        // فحص المكون
        async function checkComponent() {
            log('🔍 فحص حالة مكون المبيعات...', 'info');
            
            if (typeof window.SalesComponent === 'undefined') {
                updateStatusCard('component-status', 'error', '❌', 'المكون غير محمل');
                diagnosticData.errors.push('مكون المبيعات غير محمل');
                log('❌ مكون المبيعات غير محمل', 'error');
            } else {
                updateStatusCard('component-status', 'success', '✅', 'المكون محمل ومتاح');
                log('✅ مكون المبيعات محمل بنجاح', 'success');
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // فحص الوظائف
        async function checkFunctions() {
            log('🔍 فحص الوظائف المتاحة...', 'info');
            
            if (typeof window.SalesComponent === 'undefined') {
                updateStatusCard('functions-status', 'error', '0', 'المكون غير متاح');
                return;
            }
            
            const requiredFunctions = ['init', 'render', 'renderDashboard', 'loadSalesData', 'saveSalesData', 'diagnose', 'test'];
            let availableFunctions = 0;
            
            requiredFunctions.forEach(func => {
                if (typeof window.SalesComponent[func] === 'function') {
                    availableFunctions++;
                    log(`✅ وظيفة ${func} متاحة`, 'success');
                } else {
                    log(`❌ وظيفة ${func} مفقودة`, 'error');
                    diagnosticData.errors.push(`وظيفة ${func} مفقودة`);
                }
            });
            
            const percentage = Math.round((availableFunctions / requiredFunctions.length) * 100);
            const status = percentage === 100 ? 'success' : percentage > 70 ? 'warning' : 'error';
            
            updateStatusCard('functions-status', status, `${availableFunctions}/${requiredFunctions.length}`, 
                `${percentage}% من الوظائف متاحة`);
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // فحص البيانات
        async function checkData() {
            log('🔍 فحص حالة البيانات...', 'info');
            
            if (typeof window.SalesComponent === 'undefined') {
                updateStatusCard('data-status', 'error', '❌', 'المكون غير متاح');
                return;
            }
            
            try {
                window.SalesComponent.init();
                
                const data = window.SalesComponent.data;
                if (data) {
                    const customers = Object.keys(data.customers || {}).length;
                    const products = Object.keys(data.products || {}).length;
                    const invoices = Object.keys(data.invoices || {}).length;
                    
                    updateStatusCard('data-status', 'success', '✅', 
                        `${customers} عملاء، ${products} منتجات، ${invoices} فواتير`);
                    
                    log(`✅ البيانات متاحة: ${customers} عملاء، ${products} منتجات، ${invoices} فواتير`, 'success');
                } else {
                    updateStatusCard('data-status', 'warning', '⚠️', 'البيانات غير متاحة');
                    diagnosticData.warnings.push('البيانات غير متاحة');
                }
            } catch (error) {
                updateStatusCard('data-status', 'error', '❌', 'خطأ في تحميل البيانات');
                diagnosticData.errors.push('خطأ في تحميل البيانات: ' + error.message);
                log('❌ خطأ في فحص البيانات: ' + error.message, 'error');
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // فحص الأداء
        async function checkPerformance() {
            log('🔍 فحص أداء النظام...', 'info');
            
            const startTime = performance.now();
            
            try {
                if (window.SalesComponent) {
                    // اختبار سرعة العرض
                    window.SalesComponent.render({ view: 'dashboard' });
                    
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    
                    diagnosticData.performance.responseTime = responseTime;
                    
                    const status = responseTime < 100 ? 'success' : responseTime < 500 ? 'warning' : 'error';
                    updateStatusCard('performance-status', status, `${responseTime}ms`, 
                        `سرعة الاستجابة: ${responseTime < 100 ? 'ممتازة' : responseTime < 500 ? 'جيدة' : 'بطيئة'}`);
                    
                    log(`✅ سرعة الاستجابة: ${responseTime}ms`, 'success');
                } else {
                    updateStatusCard('performance-status', 'error', '❌', 'لا يمكن قياس الأداء');
                }
            } catch (error) {
                updateStatusCard('performance-status', 'error', '❌', 'خطأ في قياس الأداء');
                log('❌ خطأ في قياس الأداء: ' + error.message, 'error');
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // فحص الأخطاء والذاكرة
        async function checkErrorsAndMemory() {
            log('🔍 فحص الأخطاء واستخدام الذاكرة...', 'info');
            
            // عدد الأخطاء
            const errorCount = diagnosticData.errors.length;
            const errorStatus = errorCount === 0 ? 'success' : errorCount < 3 ? 'warning' : 'error';
            updateStatusCard('errors-status', errorStatus, errorCount, 
                errorCount === 0 ? 'لا توجد أخطاء' : `${errorCount} خطأ مكتشف`);
            
            // استخدام الذاكرة (تقديري)
            if (performance.memory) {
                const memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                updateStatusCard('memory-status', 'info', `${memoryUsage}MB`, 'استخدام الذاكرة الحالي');
                log(`📊 استخدام الذاكرة: ${memoryUsage}MB`, 'info');
            } else {
                updateStatusCard('memory-status', 'info', 'غير متاح', 'معلومات الذاكرة غير متاحة');
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // عرض ملخص التشخيص
        function showDiagnosticSummary() {
            const duration = Date.now() - diagnosticData.startTime;
            log(`📊 ملخص التشخيص:`, 'info');
            log(`⏱️ مدة التشخيص: ${duration}ms`, 'info');
            log(`❌ عدد الأخطاء: ${diagnosticData.errors.length}`, diagnosticData.errors.length > 0 ? 'error' : 'success');
            log(`⚠️ عدد التحذيرات: ${diagnosticData.warnings.length}`, diagnosticData.warnings.length > 0 ? 'warning' : 'success');
            
            if (diagnosticData.errors.length === 0 && diagnosticData.warnings.length === 0) {
                log('🎉 النظام يعمل بشكل مثالي!', 'success');
            }
        }

        // اختبار سريع
        function runQuickTest() {
            log('⚡ تشغيل الاختبار السريع...', 'info');
            
            if (typeof window.testSalesSystem === 'function') {
                window.testSalesSystem();
            } else if (typeof window.SalesComponent !== 'undefined' && typeof window.SalesComponent.test === 'function') {
                window.SalesComponent.test();
            } else {
                log('❌ وظيفة الاختبار غير متاحة', 'error');
            }
        }

        // إصلاح تلقائي
        function autoFix() {
            log('🔧 بدء الإصلاح التلقائي...', 'info');
            
            if (typeof window.reloadSalesSystem === 'function') {
                window.reloadSalesSystem();
                log('✅ تم تشغيل إعادة تحميل النظام', 'success');
            } else {
                log('⚠️ وظيفة الإصلاح التلقائي غير متاحة', 'warning');
            }
        }

        // إنشاء تقرير
        function generateReport() {
            log('📄 إنشاء تقرير مفصل...', 'info');
            
            const report = {
                timestamp: new Date().toISOString(),
                errors: diagnosticData.errors,
                warnings: diagnosticData.warnings,
                performance: diagnosticData.performance,
                systemStatus: 'تم إنشاء التقرير بنجاح'
            };
            
            console.log('📊 تقرير التشخيص:', report);
            log('✅ تم إنشاء التقرير في وحدة التحكم', 'success');
        }

        // بدء المراقبة
        function startMonitoring() {
            if (diagnosticData.monitoring) {
                diagnosticData.monitoring = false;
                log('⏹️ تم إيقاف المراقبة المستمرة', 'warning');
            } else {
                diagnosticData.monitoring = true;
                log('👁️ بدء المراقبة المستمرة...', 'info');
                
                setInterval(() => {
                    if (diagnosticData.monitoring) {
                        runQuickTest();
                    }
                }, 30000); // كل 30 ثانية
            }
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 بدء التشخيص التلقائي...', 'info');
                runFullDiagnostic();
            }, 1000);
        });
    </script>
</body>
</html>
