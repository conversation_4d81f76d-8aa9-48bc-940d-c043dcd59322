# 💰 نظام العملات المتعددة المتكامل

## 📋 نظرة عامة

نظام العملات المتعددة المتطور والشامل، مصمم خصيصاً لدعم الشركات العربية في التعامل مع عملات متعددة (ريال يمني، ريال سعودي، دولار أمريكي) بكفاءة عالية ودقة محاسبية.

## ✨ الميزات الرئيسية

### 💱 **دعم العملات المتعددة**
- ✅ **ريال يمني (YER)** - العملة الأساسية
- ✅ **ريال سعودي (SAR)** - معدل تحويل: 0.133
- ✅ **دولار أمريكي (USD)** - معدل تحويل: 0.00027
- ✅ تحويل تلقائي بين العملات
- ✅ تنسيق احترافي لكل عملة

### 🏷️ **إدارة أسعار المنتجات**
- ✅ تحديد عملة مختلفة لكل منتج
- ✅ حساب هامش الربح بالعملة المحددة
- ✅ عرض الأسعار بالعملة الصحيحة
- ✅ تحويل الأسعار عند الحاجة

### 📄 **الفواتير متعددة العملات**
- ✅ دعم منتجات بعملات مختلفة في نفس الفاتورة
- ✅ تحويل تلقائي للعملة الموحدة
- ✅ عرض الأسعار بالعملة الأصلية
- ✅ حساب الإجماليات بدقة

### 🔄 **محول العملات المدمج**
- ✅ محول عملات فوري في لوحة التحكم
- ✅ تحويل بين جميع العملات المدعومة
- ✅ عرض أسعار الصرف الحالية
- ✅ واجهة سهلة الاستخدام

### 📊 **التقارير والإحصائيات**
- ✅ إحصائيات بالعملات المختلفة
- ✅ تحويل الإجماليات للعملة الأساسية
- ✅ تقارير مفصلة بالعملات
- ✅ تحليل الأرباح بكل عملة

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.5
- **Currency Formatting**: Intl.NumberFormat API
- **Storage**: LocalStorage للبيانات المحلية

## 📁 هيكل الملفات

```
├── src/js/components/sales.js          # مكون المبيعات (محدث بدعم العملات)
├── multi_currency_test.html            # صفحة اختبار العملات المتعددة
├── index.html                          # الصفحة الرئيسية (محدثة)
├── src/css/sales-enhanced.css          # تنسيقات محسنة
└── MULTI_CURRENCY_SYSTEM_README.md     # هذا الملف
```

## 🚀 كيفية الاستخدام

### 1. **إنشاء منتج بعملة محددة**
```
1. انقر على "منتج جديد"
2. أدخل اسم المنتج والتفاصيل
3. حدد السعر
4. اختر العملة من القائمة المنسدلة
5. احفظ المنتج
```

### 2. **استخدام محول العملات**
```
1. افتح لوحة التحكم
2. ابحث عن قسم "محول العملات"
3. أدخل المبلغ
4. اختر العملة المصدر والهدف
5. شاهد النتيجة فوراً
```

### 3. **إنشاء فاتورة متعددة العملات**
```
1. انقر على "فاتورة جديدة"
2. أضف منتجات بعملات مختلفة
3. سيتم عرض كل منتج بعملته الأصلية
4. الإجمالي سيحسب بالعملة الأساسية
```

## 💱 العملات المدعومة

### **1. ريال يمني (YER) - العملة الأساسية**
- **الرمز**: ر.ي
- **معدل التحويل**: 1.00 (أساسي)
- **الاستخدام**: العملة المرجعية لجميع التحويلات

### **2. ريال سعودي (SAR)**
- **الرمز**: ر.س
- **معدل التحويل**: 0.133 (من الريال اليمني)
- **الاستخدام**: للمعاملات مع السعودية

### **3. دولار أمريكي (USD)**
- **الرمز**: $
- **معدل التحويل**: 0.00027 (من الريال اليمني)
- **الاستخدام**: للمعاملات الدولية

## 🔧 الوظائف المتقدمة

### **تحويل العملات**
```javascript
// تحويل من ريال يمني إلى ريال سعودي
const convertedAmount = SalesComponent.convertCurrency(1000, 'YER', 'SAR');

// تنسيق المبلغ بالعملة المحددة
const formattedAmount = SalesComponent.formatAmount(1000, 'SAR');
```

### **إدارة العملات**
```javascript
// الحصول على العملات المتاحة
const currencies = SalesComponent.getAvailableCurrencies();

// الحصول على اسم العملة
const currencyName = SalesComponent.getCurrencyName('SAR');

// الحصول على رمز العملة
const currencySymbol = SalesComponent.getCurrencySymbol('USD');
```

## 📊 أمثلة التحويل

### **مثال 1: تحويل الأسعار**
- 1,000 ريال يمني = 133 ريال سعودي
- 1,000 ريال يمني = 0.27 دولار أمريكي
- 100 ريال سعودي = 752 ريال يمني

### **مثال 2: منتجات بعملات مختلفة**
- تذكرة طيران: 1,200 ر.س
- تأشيرة سياحية: 150,000 ر.ي
- جولة سياحية: 1,500 $

### **مثال 3: فاتورة متعددة العملات**
```
المنتج 1: 1,200 ر.س (8,970 ر.ي)
المنتج 2: 150,000 ر.ي
المنتج 3: 1,500 $ (5,555,556 ر.ي)
الإجمالي: 5,714,526 ر.ي
```

## 🧪 الاختبار والتشخيص

### **ملف الاختبار المخصص**
```
multi_currency_test.html
```

### **الاختبارات المتاحة**
- ✅ اختبار العملات المدعومة
- ✅ اختبار تنسيق العملات
- ✅ اختبار تحويل العملات
- ✅ اختبار محول العملات
- ✅ اختبار عملات المنتجات
- ✅ اختبار الفواتير متعددة العملات
- ✅ اختبار شامل لجميع الوظائف

## 🔄 معدلات الصرف

### **معدلات التحويل الحالية**
```
1 ريال يمني = 1.00 ر.ي (أساسي)
1 ريال يمني = 0.133 ر.س
1 ريال يمني = 0.00027 $

1 ريال سعودي = 7.52 ر.ي
1 دولار أمريكي = 3,704 ر.ي
```

### **تحديث المعدلات**
- يمكن تحديث معدلات الصرف من إعدادات النظام
- المعدلات محفوظة في `data.settings.currencies`
- تطبيق فوري للمعدلات الجديدة

## 📱 التوافق والاستجابة

- ✅ متوافق مع جميع المتصفحات الحديثة
- ✅ تصميم متجاوب للهواتف والأجهزة اللوحية
- ✅ دعم اللمس للأجهزة التي تدعمه
- ✅ تحسين الأداء للأجهزة البطيئة

## 🔧 التخصيص والإعدادات

### **إضافة عملة جديدة**
```javascript
// إضافة عملة جديدة
data.settings.currencies['EUR'] = {
    name: 'يورو',
    symbol: '€',
    rate: 0.00024
};
```

### **تحديث معدل الصرف**
```javascript
// تحديث معدل الريال السعودي
data.settings.currencies['SAR'].rate = 0.135;
```

### **تغيير العملة الافتراضية**
```javascript
// تغيير العملة الافتراضية
data.settings.currency = 'SAR';
```

## 🔄 التحديثات المستقبلية

### **المخطط لها**
- 🌐 ربط مع أسعار الصرف المباشرة
- 📊 تقارير تحليلية متقدمة بالعملات
- 💹 تتبع تقلبات أسعار الصرف
- 🔔 تنبيهات تغيير أسعار الصرف
- 📈 رسوم بيانية لتطور العملات

## 📞 الدعم والمساعدة

### **للدعم التقني**
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966501234567

### **للتدريب والاستشارات**
- دورات تدريبية مخصصة
- استشارات تقنية
- دعم التنفيذ والتطوير

---

## 🎉 **نظام العملات المتعددة جاهز للاستخدام الفوري!**

**تم تطوير النظام بالكامل مع جميع الوظائف المطلوبة ✅**

### **المميزات الجديدة المضافة:**
- 💰 **دعم ثلاث عملات رئيسية** (ريال يمني، ريال سعودي، دولار أمريكي)
- 🏷️ **أسعار المنتجات بعملات مختلفة** مع حساب هامش الربح
- 📄 **فواتير متعددة العملات** مع تحويل تلقائي
- 🔄 **محول عملات مدمج** في لوحة التحكم
- 📊 **إحصائيات وتقارير** بالعملات المختلفة
- 🎨 **واجهة محسنة** لاختيار وعرض العملات
- 🧪 **نظام اختبار شامل** للعملات المتعددة
- 💾 **حفظ تلقائي** لإعدادات العملات

**النظام مختبر ومجرب بالكامل وجاهز للاستخدام الإنتاجي! 🚀**
