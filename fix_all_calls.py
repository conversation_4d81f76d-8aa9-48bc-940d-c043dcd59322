#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os
import sys

def fix_accounting_calls():
    """إصلاح جميع استدعاءات AccountingComponent"""

    file_path = 'src/js/components/accounting.js'

    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return False

    try:
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # عد الاستدعاءات قبل الإصلاح
        before_onclick = len(re.findall(r'onclick="AccountingComponent\.', content))
        before_onchange = len(re.findall(r'onchange="AccountingComponent\.', content))

        print(f"📊 عدد استدعاءات onclick قبل الإصلاح: {before_onclick}")
        print(f"📊 عدد استدعاءات onchange قبل الإصلاح: {before_onchange}")
        print(f"📊 إجمالي الاستدعاءات: {before_onclick + before_onchange}")

        if before_onclick == 0 and before_onchange == 0:
            print("✅ جميع الاستدعاءات مصححة بالفعل!")
            return True

        # إصلاح الاستدعاءات
        print("🔧 جاري إصلاح الاستدعاءات...")
        content = re.sub(r'onclick="AccountingComponent\.', 'onclick="window.AccountingComponent.', content)
        content = re.sub(r'onchange="AccountingComponent\.', 'onchange="window.AccountingComponent.', content)

        # عد الاستدعاءات بعد الإصلاح
        after_onclick = len(re.findall(r'onclick="AccountingComponent\.', content))
        after_onchange = len(re.findall(r'onchange="AccountingComponent\.', content))
        fixed_onclick = len(re.findall(r'onclick="window\.AccountingComponent\.', content))
        fixed_onchange = len(re.findall(r'onchange="window\.AccountingComponent\.', content))

        # كتابة الملف المحدث
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"📊 عدد استدعاءات onclick بعد الإصلاح: {after_onclick}")
        print(f"📊 عدد استدعاءات onchange بعد الإصلاح: {after_onchange}")
        print(f"✅ عدد استدعاءات onclick المصححة: {fixed_onclick}")
        print(f"✅ عدد استدعاءات onchange المصححة: {fixed_onchange}")

        if after_onclick == 0 and after_onchange == 0:
            print("🎉 تم إصلاح جميع الاستدعاءات بنجاح!")
            return True
        else:
            print(f"⚠️ لا يزال هناك {after_onclick + after_onchange} استدعاء يحتاج إصلاح")
            return False

    except Exception as e:
        print(f"❌ خطأ في إصلاح الاستدعاءات: {e}")
        return False

if __name__ == "__main__":
    success = fix_accounting_calls()
    sys.exit(0 if success else 1)
