<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف النظام المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار وظائف النظام المحاسبي</h2>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار أزرار دليل الحسابات</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2 w-100" onclick="testViewAccountDetails()">
                            اختبار عرض التفاصيل
                        </button>
                        <button class="btn btn-info mb-2 w-100" onclick="testViewAccountStatement()">
                            اختبار كشف الحساب
                        </button>
                        <button class="btn btn-danger mb-2 w-100" onclick="testDeleteAccount()">
                            اختبار حذف الحساب
                        </button>
                        <button class="btn btn-success mb-2 w-100" onclick="testEditAccount()">
                            اختبار تعديل الحساب
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Core Systems -->
    <script src="src/js/core/state.js"></script>
    <script src="src/js/core/accounting.js"></script>
    <script src="src/js/components/accounting.js"></script>

    <script>
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Accounting) {
                window.Accounting.init();
            }
            if (window.AccountingComponent) {
                window.AccountingComponent.init();
            }
            
            logResult('تم تحميل النظام المحاسبي', 'success');
        });

        function logResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 'alert-info';
            
            results.innerHTML += `
                <div class="alert ${alertClass} alert-dismissible fade show">
                    <strong>${new Date().toLocaleTimeString()}:</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function testViewAccountDetails() {
            try {
                if (window.AccountingComponent && window.AccountingComponent.viewAccountDetails) {
                    // اختبار مع حساب موجود
                    window.AccountingComponent.viewAccountDetails('1000');
                    logResult('وظيفة عرض التفاصيل تعمل بشكل صحيح', 'success');
                } else {
                    logResult('وظيفة عرض التفاصيل غير موجودة', 'error');
                }
            } catch (error) {
                logResult('خطأ في وظيفة عرض التفاصيل: ' + error.message, 'error');
            }
        }

        function testViewAccountStatement() {
            try {
                if (window.AccountingComponent && window.AccountingComponent.viewAccountStatement) {
                    window.AccountingComponent.viewAccountStatement('1000');
                    logResult('وظيفة كشف الحساب تعمل بشكل صحيح', 'success');
                } else {
                    logResult('وظيفة كشف الحساب غير موجودة', 'error');
                }
            } catch (error) {
                logResult('خطأ في وظيفة كشف الحساب: ' + error.message, 'error');
            }
        }

        function testDeleteAccount() {
            try {
                if (window.AccountingComponent && window.AccountingComponent.deleteAccount) {
                    // اختبار مع حساب فرعي (لن يتم حذفه فعلياً)
                    logResult('وظيفة حذف الحساب موجودة', 'success');
                } else {
                    logResult('وظيفة حذف الحساب غير موجودة', 'error');
                }
            } catch (error) {
                logResult('خطأ في وظيفة حذف الحساب: ' + error.message, 'error');
            }
        }

        function testEditAccount() {
            try {
                if (window.AccountingComponent && window.AccountingComponent.editAccount) {
                    window.AccountingComponent.editAccount('1000');
                    logResult('وظيفة تعديل الحساب تعمل بشكل صحيح', 'success');
                } else {
                    logResult('وظيفة تعديل الحساب غير موجودة', 'error');
                }
            } catch (error) {
                logResult('خطأ في وظيفة تعديل الحساب: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
