<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص نافذة المبيعات الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .check-container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .check-section {
            padding: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 15px 0;
            border-left: 6px solid #667eea;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .feature-card:hover::before {
            opacity: 1;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-left-color: #764ba2;
        }
        .check-button {
            width: 100%;
            margin: 12px 0;
            padding: 18px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.4s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            position: relative;
            overflow: hidden;
        }
        .check-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }
        .check-button:hover::before {
            left: 100%;
        }
        .check-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        .status-indicator {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 12px;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .status-success { background: linear-gradient(45deg, #28a745, #20c997); }
        .status-error { background: linear-gradient(45deg, #dc3545, #c82333); }
        .status-warning { background: linear-gradient(45deg, #ffc107, #fd7e14); }
        .status-info { background: linear-gradient(45deg, #17a2b8, #138496); }
        .status-pending { background: linear-gradient(45deg, #6c757d, #495057); }
        .demo-area {
            border: 3px solid #dee2e6;
            border-radius: 20px;
            padding: 25px;
            min-height: 700px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }
        .progress-container {
            background: #e9ecef;
            border-radius: 25px;
            height: 12px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-bar-custom {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 25px;
            transition: width 0.8s ease;
            position: relative;
        }
        .progress-bar-custom::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .section-title {
            color: #667eea;
            font-weight: 700;
            font-size: 1.4rem;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        .completion-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .log-area {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #00ff41;
            padding: 25px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            border: 2px solid #333;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5);
        }
        .log-area::-webkit-scrollbar {
            width: 8px;
        }
        .log-area::-webkit-scrollbar-track {
            background: #1a1a1a;
        }
        .log-area::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="header">
            <h1><i class="fas fa-search-plus me-3"></i>فحص نافذة المبيعات الشامل</h1>
            <p class="mb-0 fs-5">فحص وتطوير شامل لجميع مكونات نافذة المبيعات لضمان الاكتمال والجودة</p>
            <div class="progress-container mt-4">
                <div class="progress-bar-custom" id="overallProgress" style="width: 0%"></div>
            </div>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6" id="progressText">جاهز للبدء</span>
            </div>
        </div>

        <div class="check-section">
            <div class="row">
                <!-- لوحة الفحص -->
                <div class="col-md-4">
                    <h4 class="section-title"><i class="fas fa-clipboard-check me-2"></i>فحوصات نافذة المبيعات</h4>

                    <!-- فحص المكونات الأساسية -->
                    <div class="feature-card">
                        <div class="completion-badge">أساسي</div>
                        <h6><i class="fas fa-cogs me-2"></i>المكونات الأساسية</h6>
                        <button class="check-button" onclick="checkBasicComponents()">
                            <i class="fas fa-microchip me-2"></i>فحص تحميل المكونات
                            <span class="status-indicator" id="status-components"></span>
                        </button>
                        <button class="check-button" onclick="checkDataStructure()">
                            <i class="fas fa-database me-2"></i>فحص هيكل البيانات
                            <span class="status-indicator" id="status-data"></span>
                        </button>
                        <button class="check-button" onclick="checkCoreFunctions()">
                            <i class="fas fa-code me-2"></i>فحص الوظائف الأساسية
                            <span class="status-indicator" id="status-functions"></span>
                        </button>
                    </div>

                    <!-- فحص واجهة المستخدم -->
                    <div class="feature-card">
                        <div class="completion-badge">واجهة</div>
                        <h6><i class="fas fa-desktop me-2"></i>واجهة المستخدم</h6>
                        <button class="check-button" onclick="checkDashboardUI()">
                            <i class="fas fa-tachometer-alt me-2"></i>فحص لوحة التحكم
                            <span class="status-indicator" id="status-dashboard"></span>
                        </button>
                        <button class="check-button" onclick="checkNavigationUI()">
                            <i class="fas fa-bars me-2"></i>فحص التنقل والقوائم
                            <span class="status-indicator" id="status-navigation"></span>
                        </button>
                        <button class="check-button" onclick="checkResponsiveDesign()">
                            <i class="fas fa-mobile-alt me-2"></i>فحص التصميم المتجاوب
                            <span class="status-indicator" id="status-responsive"></span>
                        </button>
                    </div>

                    <!-- فحص الوظائف المتقدمة -->
                    <div class="feature-card">
                        <div class="completion-badge">متقدم</div>
                        <h6><i class="fas fa-rocket me-2"></i>الوظائف المتقدمة</h6>
                        <button class="check-button" onclick="checkInvoiceManagement()">
                            <i class="fas fa-file-invoice me-2"></i>فحص إدارة الفواتير
                            <span class="status-indicator" id="status-invoices"></span>
                        </button>
                        <button class="check-button" onclick="checkCustomerManagement()">
                            <i class="fas fa-users me-2"></i>فحص إدارة العملاء
                            <span class="status-indicator" id="status-customers"></span>
                        </button>
                        <button class="check-button" onclick="checkProductManagement()">
                            <i class="fas fa-box me-2"></i>فحص إدارة المنتجات
                            <span class="status-indicator" id="status-products"></span>
                        </button>
                        <button class="check-button" onclick="checkCreditNotes()">
                            <i class="fas fa-file-invoice-dollar me-2"></i>فحص الإشعارات الدائنة
                            <span class="status-indicator" id="status-creditnotes"></span>
                        </button>
                    </div>

                    <!-- فحص العملات والتقارير -->
                    <div class="feature-card">
                        <div class="completion-badge">إضافي</div>
                        <h6><i class="fas fa-chart-line me-2"></i>العملات والتقارير</h6>
                        <button class="check-button" onclick="checkMultiCurrency()">
                            <i class="fas fa-coins me-2"></i>فحص العملات المتعددة
                            <span class="status-indicator" id="status-currency"></span>
                        </button>
                        <button class="check-button" onclick="checkReportsSystem()">
                            <i class="fas fa-chart-bar me-2"></i>فحص نظام التقارير
                            <span class="status-indicator" id="status-reports"></span>
                        </button>
                        <button class="check-button" onclick="checkPrintingSystem()">
                            <i class="fas fa-print me-2"></i>فحص نظام الطباعة
                            <span class="status-indicator" id="status-printing"></span>
                        </button>
                    </div>

                    <!-- فحص الأداء والأمان -->
                    <div class="feature-card">
                        <div class="completion-badge">جودة</div>
                        <h6><i class="fas fa-shield-alt me-2"></i>الأداء والأمان</h6>
                        <button class="check-button" onclick="checkPerformance()">
                            <i class="fas fa-tachometer-alt me-2"></i>فحص الأداء
                            <span class="status-indicator" id="status-performance"></span>
                        </button>
                        <button class="check-button" onclick="checkErrorHandling()">
                            <i class="fas fa-exclamation-triangle me-2"></i>فحص معالجة الأخطاء
                            <span class="status-indicator" id="status-errors"></span>
                        </button>
                        <button class="check-button" onclick="checkDataValidation()">
                            <i class="fas fa-check-circle me-2"></i>فحص التحقق من البيانات
                            <span class="status-indicator" id="status-validation"></span>
                        </button>
                    </div>

                    <!-- فحص شامل -->
                    <div class="feature-card">
                        <div class="completion-badge">شامل</div>
                        <h6><i class="fas fa-magic me-2"></i>الفحص الشامل</h6>
                        <button class="check-button" onclick="runCompleteCheck()" style="background: linear-gradient(45deg, #dc3545, #c82333); font-size: 18px; padding: 20px;">
                            <i class="fas fa-play me-2"></i>تشغيل الفحص الشامل
                        </button>
                        <button class="check-button" onclick="generateCompletionReport()" style="background: linear-gradient(45deg, #28a745, #20c997);">
                            <i class="fas fa-file-alt me-2"></i>تقرير الاكتمال
                        </button>
                        <button class="check-button" onclick="loadSalesWindow()" style="background: linear-gradient(45deg, #007bff, #0056b3);">
                            <i class="fas fa-window-restore me-2"></i>تحميل نافذة المبيعات
                        </button>
                    </div>
                </div>

                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="demo-area" class="demo-area">
                        <div class="text-center py-5">
                            <i class="fas fa-search-plus fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة فحص نافذة المبيعات</h3>
                            <p class="text-muted fs-5">اختر فحصاً من القائمة لبدء التشغيل</p>

                            <!-- إحصائيات سريعة -->
                            <div class="row mt-5">
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                            <h5 class="card-title" id="completedChecks">0</h5>
                                            <p class="card-text text-muted">فحوصات مكتملة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                            <h5 class="card-title" id="warningChecks">0</h5>
                                            <p class="card-text text-muted">تحذيرات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                            <h5 class="card-title" id="failedChecks">0</h5>
                                            <p class="card-text text-muted">فحوصات فاشلة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                                            <h5 class="card-title" id="completionRate">0%</h5>
                                            <p class="card-text text-muted">نسبة الاكتمال</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار سريعة -->
                            <div class="mt-4">
                                <button class="btn btn-primary btn-lg me-3" onclick="loadSalesWindow()">
                                    <i class="fas fa-window-restore me-2"></i>تحميل نافذة المبيعات
                                </button>
                                <button class="btn btn-success btn-lg" onclick="runCompleteCheck()">
                                    <i class="fas fa-play me-2"></i>بدء الفحص الشامل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل النتائج -->
            <div class="mt-5">
                <h5 class="section-title"><i class="fas fa-terminal me-2"></i>سجل فحص نافذة المبيعات</h5>
                <div id="check-log" class="log-area">
                    <div class="text-success">[SYSTEM] نظام فحص نافذة المبيعات الشامل جاهز للتشغيل</div>
                    <div class="text-info">[INFO] سيتم فحص جميع مكونات نافذة المبيعات للتأكد من الاكتمال والجودة</div>
                    <div class="text-warning">[READY] اختر فحصاً لبدء التشغيل أو قم بتشغيل الفحص الشامل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>

    <script>
        // متغيرات الفحص
        let checkResults = {
            completed: 0,
            warnings: 0,
            failed: 0,
            total: 15,
            details: {}
        };

        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('check-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'color: #17a2b8',
                success: 'color: #28a745',
                warning: 'color: #ffc107',
                error: 'color: #dc3545'
            };

            const logEntry = document.createElement('div');
            logEntry.style.cssText = colors[type] || 'color: #00ff41';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الفحص
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }

            // تحديث الإحصائيات
            updateStatistics(status);
        }

        // تحديث الإحصائيات
        function updateStatistics(status) {
            if (status === 'success') {
                checkResults.completed++;
            } else if (status === 'warning') {
                checkResults.warnings++;
            } else if (status === 'error') {
                checkResults.failed++;
            }

            const completionRate = Math.round((checkResults.completed / checkResults.total) * 100);

            document.getElementById('completedChecks').textContent = checkResults.completed;
            document.getElementById('warningChecks').textContent = checkResults.warnings;
            document.getElementById('failedChecks').textContent = checkResults.failed;
            document.getElementById('completionRate').textContent = completionRate + '%';

            // تحديث شريط التقدم
            updateProgress(completionRate);
        }

        // تحديث شريط التقدم
        function updateProgress(percentage) {
            const progressBar = document.getElementById('overallProgress');
            const progressText = document.getElementById('progressText');

            progressBar.style.width = percentage + '%';

            if (percentage === 100) {
                progressText.textContent = 'اكتمل الفحص بنجاح!';
                progressText.className = 'badge bg-success text-white fs-6';
            } else if (percentage >= 75) {
                progressText.textContent = `متقدم - ${percentage}%`;
                progressText.className = 'badge bg-info text-white fs-6';
            } else if (percentage >= 50) {
                progressText.textContent = `في التقدم - ${percentage}%`;
                progressText.className = 'badge bg-warning text-dark fs-6';
            } else if (percentage > 0) {
                progressText.textContent = `بدء الفحص - ${percentage}%`;
                progressText.className = 'badge bg-primary text-white fs-6';
            }
        }

        // تحميل نافذة المبيعات
        function loadSalesWindow() {
            log('🔄 تحميل نافذة المبيعات...', 'info');

            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // تهيئة المكون
                window.SalesComponent.init();

                // عرض لوحة التحكم
                const dashboardHTML = window.SalesComponent.renderDashboard();
                document.getElementById('demo-area').innerHTML = dashboardHTML;

                log('✅ تم تحميل نافذة المبيعات بنجاح', 'success');

            } catch (error) {
                log(`❌ فشل في تحميل نافذة المبيعات: ${error.message}`, 'error');
            }
        }

        // فحص المكونات الأساسية
        function checkBasicComponents() {
            log('🧪 بدء فحص المكونات الأساسية...', 'info');
            updateStatus('components', 'pending');

            try {
                // فحص تحميل المكون الرئيسي
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // فحص الوظائف الأساسية
                const requiredFunctions = ['init', 'render', 'loadSalesData', 'saveSalesData'];
                for (const func of requiredFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`الوظيفة ${func} مفقودة`);
                    }
                }

                log('✅ جميع المكونات الأساسية محملة ومتاحة', 'success');
                updateStatus('components', 'success');

            } catch (error) {
                log(`❌ فشل فحص المكونات الأساسية: ${error.message}`, 'error');
                updateStatus('components', 'error');
            }
        }

        // فحص هيكل البيانات
        function checkDataStructure() {
            log('🧪 بدء فحص هيكل البيانات...', 'info');
            updateStatus('data', 'pending');

            try {
                if (!window.SalesComponent.data) {
                    throw new Error('هيكل البيانات غير موجود');
                }

                const requiredDataStructures = [
                    'customers', 'products', 'invoices', 'quotes',
                    'creditNotes', 'payments', 'settings'
                ];

                for (const structure of requiredDataStructures) {
                    if (!(structure in window.SalesComponent.data)) {
                        throw new Error(`هيكل البيانات ${structure} مفقود`);
                    }
                }

                log('✅ هيكل البيانات مكتمل وصحيح', 'success');
                updateStatus('data', 'success');

            } catch (error) {
                log(`❌ فشل فحص هيكل البيانات: ${error.message}`, 'error');
                updateStatus('data', 'error');
            }
        }

        // فحص الوظائف الأساسية
        function checkCoreFunctions() {
            log('🧪 بدء فحص الوظائف الأساسية...', 'info');
            updateStatus('functions', 'pending');

            try {
                // فحص وظائف العرض
                const renderFunctions = [
                    'renderDashboard', 'renderInvoicesView', 'renderCustomersView',
                    'renderProductsView', 'renderCreditNotesView'
                ];

                for (const func of renderFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة العرض ${func} مفقودة`);
                    }
                }

                // فحص وظائف الإدارة
                const managementFunctions = [
                    'showCreateInvoiceModal', 'showCreateCustomerModal',
                    'showCreateProductModal', 'showCreateCreditNoteModal'
                ];

                for (const func of managementFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة الإدارة ${func} مفقودة`);
                    }
                }

                log('✅ جميع الوظائف الأساسية متاحة وتعمل', 'success');
                updateStatus('functions', 'success');

            } catch (error) {
                log(`❌ فشل فحص الوظائف الأساسية: ${error.message}`, 'error');
                updateStatus('functions', 'error');
            }
        }

        // فحص لوحة التحكم
        function checkDashboardUI() {
            log('🧪 بدء فحص لوحة التحكم...', 'info');
            updateStatus('dashboard', 'pending');

            try {
                const dashboardHTML = window.SalesComponent.renderDashboard();

                if (!dashboardHTML || dashboardHTML.length < 100) {
                    throw new Error('لوحة التحكم فارغة أو غير مكتملة');
                }

                // فحص وجود العناصر المطلوبة
                const requiredElements = [
                    'إحصائيات', 'محول العملات', 'الإجراءات السريعة'
                ];

                for (const element of requiredElements) {
                    if (!dashboardHTML.includes(element)) {
                        log(`⚠️ عنصر "${element}" قد يكون مفقود من لوحة التحكم`, 'warning');
                    }
                }

                log('✅ لوحة التحكم مكتملة وتعمل بشكل صحيح', 'success');
                updateStatus('dashboard', 'success');

            } catch (error) {
                log(`❌ فشل فحص لوحة التحكم: ${error.message}`, 'error');
                updateStatus('dashboard', 'error');
            }
        }

        // فحص التنقل والقوائم
        function checkNavigationUI() {
            log('🧪 بدء فحص التنقل والقوائم...', 'info');
            updateStatus('navigation', 'pending');

            try {
                // فحص وظائف التنقل
                const navigationFunctions = [
                    'switchView', 'showSalesDashboard', 'showInvoices',
                    'showSalesCustomers', 'showSalesProducts'
                ];

                for (const func of navigationFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function' &&
                        typeof window[func] !== 'function') {
                        throw new Error(`وظيفة التنقل ${func} مفقودة`);
                    }
                }

                log('✅ نظام التنقل والقوائم يعمل بشكل صحيح', 'success');
                updateStatus('navigation', 'success');

            } catch (error) {
                log(`❌ فشل فحص التنقل والقوائم: ${error.message}`, 'error');
                updateStatus('navigation', 'error');
            }
        }

        // فحص التصميم المتجاوب
        function checkResponsiveDesign() {
            log('🧪 بدء فحص التصميم المتجاوب...', 'info');
            updateStatus('responsive', 'pending');

            try {
                // فحص وجود Bootstrap
                if (typeof bootstrap === 'undefined') {
                    log('⚠️ Bootstrap قد لا يكون محمل بشكل صحيح', 'warning');
                }

                // فحص CSS المتجاوب
                const hasResponsiveCSS = document.querySelector('meta[name="viewport"]');
                if (!hasResponsiveCSS) {
                    throw new Error('meta viewport مفقود');
                }

                log('✅ التصميم المتجاوب مُعد بشكل صحيح', 'success');
                updateStatus('responsive', 'success');

            } catch (error) {
                log(`❌ فشل فحص التصميم المتجاوب: ${error.message}`, 'error');
                updateStatus('responsive', 'error');
            }
        }

        // فحص إدارة الفواتير
        function checkInvoiceManagement() {
            log('🧪 بدء فحص إدارة الفواتير...', 'info');
            updateStatus('invoices', 'pending');

            try {
                // فحص وظائف الفواتير
                const invoiceFunctions = [
                    'showCreateInvoiceModal', 'saveInvoice', 'editInvoice',
                    'deleteInvoice', 'printInvoice', 'renderInvoicesView'
                ];

                for (const func of invoiceFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة الفواتير ${func} مفقودة`);
                    }
                }

                // فحص عرض الفواتير
                const invoicesHTML = window.SalesComponent.renderInvoicesView();
                if (!invoicesHTML || invoicesHTML.length < 100) {
                    throw new Error('عرض الفواتير غير مكتمل');
                }

                log('✅ نظام إدارة الفواتير مكتمل ويعمل بشكل صحيح', 'success');
                updateStatus('invoices', 'success');

            } catch (error) {
                log(`❌ فشل فحص إدارة الفواتير: ${error.message}`, 'error');
                updateStatus('invoices', 'error');
            }
        }

        // فحص إدارة العملاء
        function checkCustomerManagement() {
            log('🧪 بدء فحص إدارة العملاء...', 'info');
            updateStatus('customers', 'pending');

            try {
                // فحص وظائف العملاء
                const customerFunctions = [
                    'showCreateCustomerModal', 'saveCustomer', 'editCustomer',
                    'deleteCustomer', 'viewCustomer', 'renderCustomersView'
                ];

                for (const func of customerFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة العملاء ${func} مفقودة`);
                    }
                }

                log('✅ نظام إدارة العملاء مكتمل ويعمل بشكل صحيح', 'success');
                updateStatus('customers', 'success');

            } catch (error) {
                log(`❌ فشل فحص إدارة العملاء: ${error.message}`, 'error');
                updateStatus('customers', 'error');
            }
        }

        // فحص إدارة المنتجات
        function checkProductManagement() {
            log('🧪 بدء فحص إدارة المنتجات...', 'info');
            updateStatus('products', 'pending');

            try {
                // فحص وظائف المنتجات
                const productFunctions = [
                    'showCreateProductModal', 'saveProduct', 'editProduct',
                    'deleteProduct', 'viewProduct', 'renderProductsView'
                ];

                for (const func of productFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة المنتجات ${func} مفقودة`);
                    }
                }

                log('✅ نظام إدارة المنتجات مكتمل ويعمل بشكل صحيح', 'success');
                updateStatus('products', 'success');

            } catch (error) {
                log(`❌ فشل فحص إدارة المنتجات: ${error.message}`, 'error');
                updateStatus('products', 'error');
            }
        }

        // فحص الإشعارات الدائنة
        function checkCreditNotes() {
            log('🧪 بدء فحص الإشعارات الدائنة...', 'info');
            updateStatus('creditnotes', 'pending');

            try {
                // فحص وظائف الإشعارات الدائنة
                const creditNoteFunctions = [
                    'showCreateCreditNoteModal', 'saveCreditNote', 'editCreditNote',
                    'deleteCreditNote', 'viewCreditNote', 'renderCreditNotesView'
                ];

                for (const func of creditNoteFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة الإشعارات الدائنة ${func} مفقودة`);
                    }
                }

                log('✅ نظام الإشعارات الدائنة مكتمل ويعمل بشكل صحيح', 'success');
                updateStatus('creditnotes', 'success');

            } catch (error) {
                log(`❌ فشل فحص الإشعارات الدائنة: ${error.message}`, 'error');
                updateStatus('creditnotes', 'error');
            }
        }

        // فحص العملات المتعددة
        function checkMultiCurrency() {
            log('🧪 بدء فحص العملات المتعددة...', 'info');
            updateStatus('currency', 'pending');

            try {
                // فحص وظائف العملات
                const currencyFunctions = [
                    'formatAmount', 'convertCurrency', 'getAvailableCurrencies',
                    'getCurrencyName', 'getCurrencySymbol'
                ];

                for (const func of currencyFunctions) {
                    if (typeof window.SalesComponent[func] !== 'function') {
                        throw new Error(`وظيفة العملات ${func} مفقودة`);
                    }
                }

                // فحص العملات المدعومة
                const currencies = window.SalesComponent.getAvailableCurrencies();
                if (!currencies.YER || !currencies.SAR || !currencies.USD) {
                    throw new Error('العملات المطلوبة غير متاحة');
                }

                log('✅ نظام العملات المتعددة مكتمل ويعمل بشكل صحيح', 'success');
                updateStatus('currency', 'success');

            } catch (error) {
                log(`❌ فشل فحص العملات المتعددة: ${error.message}`, 'error');
                updateStatus('currency', 'error');
            }
        }

        // فحص نظام التقارير
        function checkReportsSystem() {
            log('🧪 بدء فحص نظام التقارير...', 'info');
            updateStatus('reports', 'pending');

            try {
                // فحص وظائف التقارير الأساسية
                const reportFunctions = ['showReportsModal', 'generateReport', 'exportReport'];
                let foundFunctions = 0;

                for (const func of reportFunctions) {
                    if (typeof window.SalesComponent[func] === 'function') {
                        foundFunctions++;
                    }
                }

                if (foundFunctions === 0) {
                    log('⚠️ نظام التقارير قيد التطوير', 'warning');
                    updateStatus('reports', 'warning');
                } else {
                    log('✅ نظام التقارير متاح ويعمل', 'success');
                    updateStatus('reports', 'success');
                }

            } catch (error) {
                log(`❌ فشل فحص نظام التقارير: ${error.message}`, 'error');
                updateStatus('reports', 'error');
            }
        }

        // فحص نظام الطباعة
        function checkPrintingSystem() {
            log('🧪 بدء فحص نظام الطباعة...', 'info');
            updateStatus('printing', 'pending');

            try {
                // فحص وظائف الطباعة
                const printFunctions = [
                    'printInvoice', 'printCreditNote', 'downloadInvoicePDF'
                ];

                let foundFunctions = 0;
                for (const func of printFunctions) {
                    if (typeof window.SalesComponent[func] === 'function') {
                        foundFunctions++;
                    }
                }

                if (foundFunctions >= 2) {
                    log('✅ نظام الطباعة مكتمل ويعمل بشكل صحيح', 'success');
                    updateStatus('printing', 'success');
                } else {
                    log('⚠️ بعض وظائف الطباعة قد تكون مفقودة', 'warning');
                    updateStatus('printing', 'warning');
                }

            } catch (error) {
                log(`❌ فشل فحص نظام الطباعة: ${error.message}`, 'error');
                updateStatus('printing', 'error');
            }
        }

        // فحص الأداء
        function checkPerformance() {
            log('🧪 بدء فحص الأداء...', 'info');
            updateStatus('performance', 'pending');

            try {
                const startTime = performance.now();

                // اختبار سرعة تحميل البيانات
                window.SalesComponent.loadSalesData();

                // اختبار سرعة عرض لوحة التحكم
                window.SalesComponent.renderDashboard();

                const endTime = performance.now();
                const loadTime = endTime - startTime;

                if (loadTime < 100) {
                    log(`✅ الأداء ممتاز - وقت التحميل: ${loadTime.toFixed(2)}ms`, 'success');
                    updateStatus('performance', 'success');
                } else if (loadTime < 500) {
                    log(`⚠️ الأداء جيد - وقت التحميل: ${loadTime.toFixed(2)}ms`, 'warning');
                    updateStatus('performance', 'warning');
                } else {
                    log(`❌ الأداء بطيء - وقت التحميل: ${loadTime.toFixed(2)}ms`, 'error');
                    updateStatus('performance', 'error');
                }

            } catch (error) {
                log(`❌ فشل فحص الأداء: ${error.message}`, 'error');
                updateStatus('performance', 'error');
            }
        }

        // فحص معالجة الأخطاء
        function checkErrorHandling() {
            log('🧪 بدء فحص معالجة الأخطاء...', 'info');
            updateStatus('errors', 'pending');

            try {
                // فحص وجود وظائف معالجة الأخطاء
                const errorFunctions = ['handleError', 'showNotification', 'showError'];
                let foundFunctions = 0;

                for (const func of errorFunctions) {
                    if (typeof window.SalesComponent[func] === 'function') {
                        foundFunctions++;
                    }
                }

                if (foundFunctions >= 2) {
                    log('✅ نظام معالجة الأخطاء متاح ويعمل', 'success');
                    updateStatus('errors', 'success');
                } else {
                    log('⚠️ نظام معالجة الأخطاء قد يحتاج تحسين', 'warning');
                    updateStatus('errors', 'warning');
                }

            } catch (error) {
                log(`❌ فشل فحص معالجة الأخطاء: ${error.message}`, 'error');
                updateStatus('errors', 'error');
            }
        }

        // فحص التحقق من البيانات
        function checkDataValidation() {
            log('🧪 بدء فحص التحقق من البيانات...', 'info');
            updateStatus('validation', 'pending');

            try {
                // فحص وجود وظائف التحقق
                const validationFunctions = ['validateInvoice', 'validateCustomer', 'validateProduct'];
                let foundFunctions = 0;

                for (const func of validationFunctions) {
                    if (typeof window.SalesComponent[func] === 'function') {
                        foundFunctions++;
                    }
                }

                // فحص التحقق في النماذج
                if (foundFunctions > 0) {
                    log('✅ نظام التحقق من البيانات متاح', 'success');
                    updateStatus('validation', 'success');
                } else {
                    log('⚠️ نظام التحقق من البيانات قد يحتاج تطوير', 'warning');
                    updateStatus('validation', 'warning');
                }

            } catch (error) {
                log(`❌ فشل فحص التحقق من البيانات: ${error.message}`, 'error');
                updateStatus('validation', 'error');
            }
        }

        // تشغيل الفحص الشامل
        async function runCompleteCheck() {
            log('🚀 بدء الفحص الشامل لنافذة المبيعات...', 'info');

            // إعادة تعيين الإحصائيات
            checkResults = { completed: 0, warnings: 0, failed: 0, total: 15, details: {} };
            updateStatistics('reset');

            const checks = [
                { name: 'المكونات الأساسية', func: checkBasicComponents },
                { name: 'هيكل البيانات', func: checkDataStructure },
                { name: 'الوظائف الأساسية', func: checkCoreFunctions },
                { name: 'لوحة التحكم', func: checkDashboardUI },
                { name: 'التنقل والقوائم', func: checkNavigationUI },
                { name: 'التصميم المتجاوب', func: checkResponsiveDesign },
                { name: 'إدارة الفواتير', func: checkInvoiceManagement },
                { name: 'إدارة العملاء', func: checkCustomerManagement },
                { name: 'إدارة المنتجات', func: checkProductManagement },
                { name: 'الإشعارات الدائنة', func: checkCreditNotes },
                { name: 'العملات المتعددة', func: checkMultiCurrency },
                { name: 'نظام التقارير', func: checkReportsSystem },
                { name: 'نظام الطباعة', func: checkPrintingSystem },
                { name: 'الأداء', func: checkPerformance },
                { name: 'معالجة الأخطاء', func: checkErrorHandling },
                { name: 'التحقق من البيانات', func: checkDataValidation }
            ];

            for (let i = 0; i < checks.length; i++) {
                const check = checks[i];
                log(`🔄 تشغيل فحص: ${check.name}`, 'info');
                await check.func();
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            log('🎉 اكتمل الفحص الشامل لنافذة المبيعات', 'success');
            generateCompletionReport();
        }

        // تقرير الاكتمال
        function generateCompletionReport() {
            log('📋 إنشاء تقرير الاكتمال...', 'info');

            const completionRate = Math.round((checkResults.completed / checkResults.total) * 100);

            log('', 'info');
            log('═══════════════════════════════════════', 'info');
            log('📊 تقرير اكتمال نافذة المبيعات', 'success');
            log('═══════════════════════════════════════', 'info');
            log(`✅ فحوصات مكتملة: ${checkResults.completed}/${checkResults.total}`, 'success');
            log(`⚠️ تحذيرات: ${checkResults.warnings}`, 'warning');
            log(`❌ فحوصات فاشلة: ${checkResults.failed}`, 'error');
            log(`📈 نسبة الاكتمال: ${completionRate}%`, 'info');
            log('═══════════════════════════════════════', 'info');

            if (completionRate >= 90) {
                log('🎉 نافذة المبيعات مكتملة وجاهزة للاستخدام النهائي!', 'success');
            } else if (completionRate >= 75) {
                log('👍 نافذة المبيعات في حالة جيدة مع بعض التحسينات المطلوبة', 'warning');
            } else {
                log('🔧 نافذة المبيعات تحتاج المزيد من التطوير', 'error');
            }

            log('', 'info');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام المبيعات...', 'info');

                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                    log('💡 تأكد من تحميل ملف src/js/components/sales.js', 'warning');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('📋 جاهز لبدء فحص نافذة المبيعات الشامل', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>