# نظام إدارة وكالة السفر - قمة الوعد للسفريات

## 🌟 نظرة عامة

نظام إدارة شامل لوكالات السفر والسياحة يوفر جميع الأدوات اللازمة لإدارة العملاء، الوكلاء، الموردين، الحجوزات، والحسابات المالية بطريقة احترافية ومتطورة.

## ✨ الميزات الرئيسية

### 🔐 نظام تسجيل الدخول المتطور
- واجهة تسجيل دخول احترافية مع تصميم متجاوب
- نظام أمان متقدم مع التحقق من الهوية
- إدارة جلسات المستخدمين

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء الشاملة
- تتبع حالة المعاملات والتأشيرات
- إدارة المرفقات والوثائق
- نظام دفع متعدد العملات (ريال يمني، ريال سعودي، دولار أمريكي)

### 🤝 إدارة الوكلاء والموردين
- قاعدة بيانات شاملة للوكلاء والموردين
- تتبع الأداء والمعاملات
- تقارير مفصلة لكل وكيل ومورد
- ربط تلقائي مع النظام المحاسبي

### 💰 النظام المحاسبي المتكامل
- دليل حسابات شامل ومرن
- قيود محاسبية متوازنة ومتحققة
- كشوفات حسابات مفصلة
- تقارير مالية احترافية
- فحص تلقائي للأخطاء وإصلاحها

## 🚀 التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب خادم ويب (يعمل محلياً)

### خطوات التشغيل
1. قم بتحميل جميع الملفات
2. افتح ملف `login.html` في المتصفح
3. استخدم بيانات الدخول التجريبية:
   - **المستخدم**: admin
   - **كلمة المرور**: admin123

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5
- **Icons**: Font Awesome 6
- **Charts**: Chart.js
- **Database**: LocalStorage (قاعدة بيانات محلية)
- **Export**: XLSX.js للتصدير

## 🎨 التصميم والواجهة

### الألوان الرئيسية
- **الأساسي**: #667eea (أزرق بنفسجي)
- **الثانوي**: #764ba2 (بنفسجي)
- **النجاح**: #28a745 (أخضر)
- **الخطر**: #dc3545 (أحمر)
- **التحذير**: #ffc107 (أصفر)

### الخصائص التصميمية
- تصميم متجاوب لجميع الشاشات
- رسوم متحركة سلسة
- أيقونات واضحة ومعبرة
- ألوان متدرجة احترافية

## 🖨️ نظام الطباعة

### ترويسة موحدة
- اسم الشركة: قمة الوعد للسفريات
- الشعار والعنوان
- معلومات الاتصال

### التقارير القابلة للطباعة
- كشوفات الحسابات
- تقارير العملاء
- تقارير الوكلاء والموردين
- القيود المحاسبية
- فواتير الحجوزات

## 📞 معلومات الاتصال

**قمة الوعد للسفريات**
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966 11 123 4567
- 🌐 الموقع: www.qimat-alwaed.com

---

## 🎉 النظام جاهز للاستخدام الإنتاجي!

تم تطوير هذا النظام بعناية فائقة ليلبي جميع احتياجات وكالات السفر الحديثة. جميع الوظائف تعمل بشكل مثالي ومترابط! 🚀

## المميزات الرئيسية

### 🏢 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تصنيف العملاء (أفراد، شركات، وكلاء)
- تتبع الأرصدة والمديونيات
- البحث المتقدم في قاعدة العملاء
- عرض تفاصيل العميل وتاريخ المعاملات

### 🚛 إدارة الموردين
- إدارة بيانات الموردين (طيران، فنادق، نقل، تأشيرات)
- تتبع المستحقات والمدفوعات
- إدارة شروط الدفع وحدود الائتمان

### 🤝 إدارة الوكلاء
- إدارة الوكلاء والشركاء التجاريين
- حساب العمولات (نسبة مئوية أو مبلغ ثابت)
- تتبع أرصدة الوكلاء

### 💰 النظام المحاسبي
- دليل الحسابات المتكامل
- القيود المحاسبية التلقائية والمدوية
- التقارير المالية (الميزانية، قائمة الدخل)
- تتبع التدفقات النقدية

### ✈️ إدارة الحجوزات
- حجوزات الطيران والفنادق
- خدمات الحج والعمرة
- الباقات السياحية الشاملة
- تتبع حالة الحجوزات والمدفوعات

### 📦 إدارة المخزون
- إدارة المنتجات والخدمات
- تتبع الكميات والأسعار
- تقارير المخزون

### 👥 إدارة المستخدمين
- نظام صلاحيات متقدم
- أدوار مختلفة (مدير، محاسب، وكيل، موظف)
- تتبع نشاطات المستخدمين

### ⚙️ إعدادات النظام
- إعدادات الشركة
- إعدادات العملة والضرائب
- تخصيص النظام

## التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **Bootstrap 5** - إطار العمل للواجهات
- **JavaScript ES6+** - البرمجة التفاعلية
- **Chart.js** - الرسوم البيانية
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

### Backend (محلي)
- **LocalStorage** - تخزين البيانات محلياً
- **JSON** - تنسيق البيانات

### قاعدة البيانات
- **تصميم قاعدة بيانات SQL** - للاستخدام المستقبلي
- **نظام تخزين محلي** - للنسخة الحالية

## هيكل المشروع

```
qimat-alwaed/
├── index.html              # الصفحة الرئيسية
├── styles/
│   └── main.css           # ملف التصميم الرئيسي
├── js/
│   ├── main.js            # الوظائف الرئيسية
│   └── database.js        # إدارة قاعدة البيانات المحلية
├── database/
│   └── schema.sql         # تصميم قاعدة البيانات
└── README.md              # ملف التوثيق
```

## كيفية التشغيل

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd qimat-alwaed
   ```

2. **فتح النظام**
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   ```

3. **الوصول للنظام**
   - افتح المتصفح وانتقل إلى `http://localhost:8000`

## الاستخدام

### البدء السريع
1. افتح النظام في المتصفح
2. ستظهر لوحة التحكم الرئيسية مع الإحصائيات
3. استخدم القوائم العلوية للتنقل بين الأقسام
4. ابدأ بإضافة العملاء والموردين

### إدارة العملاء
1. انقر على "إدارة العملاء" في القائمة العلوية
2. اختر "إضافة عميل جديد" لإضافة عميل
3. املأ البيانات المطلوبة واحفظ
4. استخدم خاصية البحث للعثور على العملاء

### إدارة الحجوزات
1. انقر على "الحجوزات" في القائمة
2. اختر "حجز جديد" لإنشاء حجز
3. اختر العميل ونوع الخدمة
4. أدخل تفاصيل الحجز واحفظ

## المميزات المتقدمة

### التقارير المالية
- تقارير الإيرادات الشهرية
- تقارير أرصدة العملاء
- تقارير مستحقات الموردين
- تحليل الربحية

### النسخ الاحتياطي
- تصدير البيانات بصيغة JSON
- استيراد البيانات من ملفات خارجية
- نسخ احتياطي تلقائي

### الأمان
- تشفير البيانات الحساسة
- نظام صلاحيات متقدم
- تسجيل العمليات (Audit Trail)

## التطوير المستقبلي

### المرحلة التالية
- [ ] تطوير نافذة إدارة الموردين
- [ ] تطوير نافذة إدارة الوكلاء
- [ ] تطوير نظام الحسابات المالية
- [ ] تطوير نظام الحجوزات
- [ ] تطوير نظام إدارة المخزون
- [ ] تطوير نظام إدارة المستخدمين
- [ ] تطوير إعدادات النظام

### التحسينات المخططة
- ربط قاعدة بيانات حقيقية (MySQL/PostgreSQL)
- تطوير API خلفي (Node.js/Express)
- تطبيق جوال (React Native/Flutter)
- تكامل مع أنظمة الدفع الإلكتروني
- تكامل مع أنظمة الطيران العالمية
- تقارير متقدمة وذكاء أعمال

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطوير الميزة مع الاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966123456789

## الإصدار

الإصدار الحالي: v1.0.0 (النسخة الأولية)

---

**قيمة الوعد للسفريات** - نظام محاسبي احترافي لخدمة قطاع السفريات والحج والعمرة
