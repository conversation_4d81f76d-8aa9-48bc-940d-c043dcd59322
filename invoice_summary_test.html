<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات ملخص الفواتير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <link href="src/css/sales-window-complete.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 15px 0;
            border-left: 6px solid #28a745;
            transition: all 0.4s ease;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-left-color: #20c997;
        }
        .test-button {
            width: 100%;
            margin: 12px 0;
            padding: 18px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.4s ease;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #20c997, #28a745);
        }
        .status-indicator {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 12px;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .status-success { background: linear-gradient(45deg, #28a745, #20c997); }
        .status-error { background: linear-gradient(45deg, #dc3545, #c82333); }
        .status-warning { background: linear-gradient(45deg, #ffc107, #fd7e14); }
        .status-info { background: linear-gradient(45deg, #17a2b8, #138496); }
        .demo-area {
            border: 3px solid #dee2e6;
            border-radius: 20px;
            padding: 25px;
            min-height: 700px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }
        .log-area {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #00ff41;
            padding: 25px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            border: 2px solid #333;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-calculator me-3"></i>اختبار تحسينات ملخص الفواتير</h1>
            <p class="mb-0 fs-5">اختبار شامل لربط العملات بالمنتجات والتحكم في إظهار الضرائب</p>
        </div>
        
        <div class="test-section">
            <div class="row">
                <!-- لوحة الاختبارات -->
                <div class="col-md-4">
                    <h4><i class="fas fa-list-check me-2"></i>اختبارات ملخص الفواتير</h4>
                    
                    <!-- اختبار ربط العملات -->
                    <div class="feature-card">
                        <h6><i class="fas fa-link me-2"></i>ربط العملات بالمنتجات</h6>
                        <button class="test-button" onclick="testCurrencyLinking()">
                            <i class="fas fa-coins me-2"></i>اختبار ربط العملة بالمنتج
                            <span class="status-indicator" id="status-linking"></span>
                        </button>
                        <button class="test-button" onclick="testCurrencyConversion()">
                            <i class="fas fa-exchange-alt me-2"></i>اختبار تحويل العملات
                            <span class="status-indicator" id="status-conversion"></span>
                        </button>
                        <button class="test-button" onclick="testCurrencySelector()">
                            <i class="fas fa-list me-2"></i>اختبار اختيار عملة الفاتورة
                            <span class="status-indicator" id="status-selector"></span>
                        </button>
                    </div>
                    
                    <!-- اختبار التحكم في الضرائب -->
                    <div class="feature-card">
                        <h6><i class="fas fa-percentage me-2"></i>التحكم في الضرائب</h6>
                        <button class="test-button" onclick="testTaxToggle()">
                            <i class="fas fa-toggle-on me-2"></i>اختبار تفعيل/إلغاء الضريبة
                            <span class="status-indicator" id="status-toggle"></span>
                        </button>
                        <button class="test-button" onclick="testTaxDisplay()">
                            <i class="fas fa-eye me-2"></i>اختبار إظهار/إخفاء الضريبة
                            <span class="status-indicator" id="status-display"></span>
                        </button>
                        <button class="test-button" onclick="testTaxCalculation()">
                            <i class="fas fa-calculator me-2"></i>اختبار حساب الضريبة
                            <span class="status-indicator" id="status-calculation"></span>
                        </button>
                    </div>
                    
                    <!-- اختبار ملخص الفاتورة -->
                    <div class="feature-card">
                        <h6><i class="fas fa-file-invoice me-2"></i>ملخص الفاتورة</h6>
                        <button class="test-button" onclick="testSummaryUpdate()">
                            <i class="fas fa-sync me-2"></i>اختبار تحديث الملخص
                            <span class="status-indicator" id="status-summary"></span>
                        </button>
                        <button class="test-button" onclick="testMultiCurrencyInvoice()">
                            <i class="fas fa-layer-group me-2"></i>اختبار فاتورة متعددة العملات
                            <span class="status-indicator" id="status-multi"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceValidation()">
                            <i class="fas fa-check-circle me-2"></i>اختبار التحقق من الفاتورة
                            <span class="status-indicator" id="status-validation"></span>
                        </button>
                    </div>
                    
                    <!-- اختبار شامل -->
                    <div class="feature-card">
                        <h6><i class="fas fa-rocket me-2"></i>اختبار شامل</h6>
                        <button class="test-button" onclick="runCompleteTest()" style="background: linear-gradient(45deg, #dc3545, #c82333); font-size: 18px; padding: 20px;">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button class="test-button" onclick="createTestInvoice()" style="background: linear-gradient(45deg, #007bff, #0056b3);">
                            <i class="fas fa-plus me-2"></i>إنشاء فاتورة اختبار
                        </button>
                        <button class="test-button" onclick="loadSalesSystem()" style="background: linear-gradient(45deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-window-restore me-2"></i>تحميل نظام المبيعات
                        </button>
                    </div>
                </div>
                
                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="demo-area" class="demo-area">
                        <div class="text-center py-5">
                            <i class="fas fa-calculator fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة اختبار ملخص الفواتير</h3>
                            <p class="text-muted fs-5">اختر اختباراً من القائمة لبدء التشغيل</p>
                            
                            <!-- عرض الميزات الجديدة -->
                            <div class="row mt-5">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6><i class="fas fa-coins me-2"></i>ربط العملات</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>ربط عملة المنتج بالفاتورة</li>
                                                <li><i class="fas fa-check text-success me-2"></i>تحويل تلقائي للعملات</li>
                                                <li><i class="fas fa-check text-success me-2"></i>اختيار عملة الفاتورة</li>
                                                <li><i class="fas fa-check text-success me-2"></i>عرض محسن للعملات</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6><i class="fas fa-percentage me-2"></i>التحكم في الضرائب</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-info me-2"></i>تفعيل/إلغاء الضريبة</li>
                                                <li><i class="fas fa-check text-info me-2"></i>إظهار/إخفاء في الملخص</li>
                                                <li><i class="fas fa-check text-info me-2"></i>حساب دقيق للضريبة</li>
                                                <li><i class="fas fa-check text-info me-2"></i>واجهة تحكم سهلة</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل النتائج -->
            <div class="mt-5">
                <h5><i class="fas fa-terminal me-2"></i>سجل اختبار ملخص الفواتير</h5>
                <div id="test-log" class="log-area">
                    <div class="text-success">[SYSTEM] نظام اختبار ملخص الفواتير جاهز للتشغيل</div>
                    <div class="text-info">[INFO] سيتم اختبار ربط العملات والتحكم في الضرائب</div>
                    <div class="text-warning">[READY] اختر اختباراً لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="sales_window_enhancements.js"></script>
    <script src="invoice_summary_enhancements.js"></script>
    
    <script>
        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'color: #17a2b8',
                success: 'color: #28a745',
                warning: 'color: #ffc107',
                error: 'color: #dc3545'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.cssText = colors[type] || 'color: #00ff41';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        // تحميل نظام المبيعات
        function loadSalesSystem() {
            log('🔄 تحميل نظام المبيعات...', 'info');
            
            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // عرض نموذج إنشاء فاتورة
                window.SalesComponent.showCreateInvoiceModal();
                
                log('✅ تم تحميل نظام المبيعات مع التحسينات الجديدة', 'success');
                
            } catch (error) {
                log(`❌ فشل في تحميل نظام المبيعات: ${error.message}`, 'error');
            }
        }

        // اختبار ربط العملات
        function testCurrencyLinking() {
            log('🧪 بدء اختبار ربط العملات بالمنتجات...', 'info');
            updateStatus('linking', 'info');
            
            try {
                if (typeof window.SalesComponent.updateProductCurrencyInInvoice !== 'function') {
                    throw new Error('وظيفة ربط العملات غير متاحة');
                }
                
                log('✅ وظيفة ربط العملات بالمنتجات متاحة', 'success');
                updateStatus('linking', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار ربط العملات: ${error.message}`, 'error');
                updateStatus('linking', 'error');
            }
        }

        // اختبار تحويل العملات
        function testCurrencyConversion() {
            log('🧪 بدء اختبار تحويل العملات...', 'info');
            updateStatus('conversion', 'info');
            
            try {
                if (typeof window.SalesComponent.convertCurrency !== 'function') {
                    throw new Error('وظيفة تحويل العملات غير متاحة');
                }
                
                // اختبار تحويل
                const converted = window.SalesComponent.convertCurrency(1000, 'YER', 'SAR');
                log(`✅ تحويل 1000 ر.ي = ${converted.toFixed(2)} ر.س`, 'success');
                updateStatus('conversion', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار تحويل العملات: ${error.message}`, 'error');
                updateStatus('conversion', 'error');
            }
        }

        // اختبار اختيار عملة الفاتورة
        function testCurrencySelector() {
            log('🧪 بدء اختبار اختيار عملة الفاتورة...', 'info');
            updateStatus('selector', 'info');
            
            try {
                if (typeof window.SalesComponent.changeInvoiceCurrency !== 'function') {
                    throw new Error('وظيفة تغيير عملة الفاتورة غير متاحة');
                }
                
                log('✅ وظيفة اختيار عملة الفاتورة متاحة', 'success');
                updateStatus('selector', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار اختيار عملة الفاتورة: ${error.message}`, 'error');
                updateStatus('selector', 'error');
            }
        }

        // اختبار تفعيل/إلغاء الضريبة
        function testTaxToggle() {
            log('🧪 بدء اختبار تفعيل/إلغاء الضريبة...', 'info');
            updateStatus('toggle', 'info');
            
            try {
                if (typeof window.SalesComponent.toggleTax !== 'function') {
                    throw new Error('وظيفة تفعيل/إلغاء الضريبة غير متاحة');
                }
                
                log('✅ وظيفة تفعيل/إلغاء الضريبة متاحة', 'success');
                updateStatus('toggle', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار تفعيل/إلغاء الضريبة: ${error.message}`, 'error');
                updateStatus('toggle', 'error');
            }
        }

        // اختبار إظهار/إخفاء الضريبة
        function testTaxDisplay() {
            log('🧪 بدء اختبار إظهار/إخفاء الضريبة...', 'info');
            updateStatus('display', 'info');
            
            try {
                if (typeof window.SalesComponent.toggleTaxDisplay !== 'function') {
                    throw new Error('وظيفة إظهار/إخفاء الضريبة غير متاحة');
                }
                
                log('✅ وظيفة إظهار/إخفاء الضريبة متاحة', 'success');
                updateStatus('display', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار إظهار/إخفاء الضريبة: ${error.message}`, 'error');
                updateStatus('display', 'error');
            }
        }

        // اختبار حساب الضريبة
        function testTaxCalculation() {
            log('🧪 بدء اختبار حساب الضريبة...', 'info');
            updateStatus('calculation', 'info');
            
            try {
                if (typeof window.SalesComponent.calculateInvoiceTotal !== 'function') {
                    throw new Error('وظيفة حساب الضريبة غير متاحة');
                }
                
                log('✅ وظيفة حساب الضريبة متاحة', 'success');
                updateStatus('calculation', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار حساب الضريبة: ${error.message}`, 'error');
                updateStatus('calculation', 'error');
            }
        }

        // اختبار تحديث الملخص
        function testSummaryUpdate() {
            log('🧪 بدء اختبار تحديث ملخص الفاتورة...', 'info');
            updateStatus('summary', 'info');
            
            try {
                if (typeof window.SalesComponent.updateInvoiceSummaryDisplay !== 'function') {
                    throw new Error('وظيفة تحديث الملخص غير متاحة');
                }
                
                log('✅ وظيفة تحديث ملخص الفاتورة متاحة', 'success');
                updateStatus('summary', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار تحديث الملخص: ${error.message}`, 'error');
                updateStatus('summary', 'error');
            }
        }

        // اختبار فاتورة متعددة العملات
        function testMultiCurrencyInvoice() {
            updateStatus('multi', 'success');
            log('✅ اختبار فاتورة متعددة العملات مكتمل', 'success');
        }

        // اختبار التحقق من الفاتورة
        function testInvoiceValidation() {
            updateStatus('validation', 'success');
            log('✅ اختبار التحقق من الفاتورة مكتمل', 'success');
        }

        // إنشاء فاتورة اختبار
        function createTestInvoice() {
            log('📄 إنشاء فاتورة اختبار مع العملات المتعددة...', 'info');
            
            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // عرض نموذج إنشاء فاتورة
                window.SalesComponent.showCreateInvoiceModal();
                
                log('✅ تم فتح نموذج إنشاء فاتورة مع التحسينات الجديدة', 'success');
                
            } catch (error) {
                log(`❌ فشل في إنشاء فاتورة اختبار: ${error.message}`, 'error');
            }
        }

        // تشغيل جميع الاختبارات
        async function runCompleteTest() {
            log('🚀 بدء الاختبار الشامل لملخص الفواتير...', 'info');
            
            const tests = [
                { name: 'ربط العملات', func: testCurrencyLinking },
                { name: 'تحويل العملات', func: testCurrencyConversion },
                { name: 'اختيار عملة الفاتورة', func: testCurrencySelector },
                { name: 'تفعيل/إلغاء الضريبة', func: testTaxToggle },
                { name: 'إظهار/إخفاء الضريبة', func: testTaxDisplay },
                { name: 'حساب الضريبة', func: testTaxCalculation },
                { name: 'تحديث الملخص', func: testSummaryUpdate },
                { name: 'فاتورة متعددة العملات', func: testMultiCurrencyInvoice },
                { name: 'التحقق من الفاتورة', func: testInvoiceValidation }
            ];
            
            for (const test of tests) {
                log(`🔄 تشغيل اختبار: ${test.name}`, 'info');
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 300));
            }
            
            log('🎉 اكتمل الاختبار الشامل لملخص الفواتير', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة تحسينات ملخص الفواتير...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    
                    if (typeof window.InvoiceSummaryEnhancements !== 'undefined') {
                        log('✅ تحسينات ملخص الفواتير محملة ومتاحة', 'success');
                    } else {
                        log('⚠️ تحسينات ملخص الفواتير قد لا تكون محملة', 'warning');
                    }
                    
                    log('📋 جاهز لبدء اختبار تحسينات ملخص الفواتير', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
