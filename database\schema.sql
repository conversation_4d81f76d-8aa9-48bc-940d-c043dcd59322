-- قاعدة بيانات نظام قيمة الوعد المحاسبي
-- نظام محاسبي لمكاتب السفريات والحج والعمرة

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS qimat_alwaed_accounting 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE qimat_alwaed_accounting;

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'accountant', 'agent', 'employee') DEFAULT 'employee',
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    national_id VARCHAR(20),
    passport_number VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'السعودية',
    customer_type ENUM('individual', 'company', 'agent') DEFAULT 'individual',
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول الموردين
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    supplier_type ENUM('airline', 'hotel', 'transport', 'visa', 'insurance', 'other') DEFAULT 'other',
    payment_terms VARCHAR(100),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول الوكلاء
CREATE TABLE agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    commission_rate DECIMAL(5,2) DEFAULT 0,
    commission_type ENUM('percentage', 'fixed') DEFAULT 'percentage',
    payment_terms VARCHAR(100),
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول دليل الحسابات
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    parent_account_id INT NULL,
    level INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
);

-- جدول القيود المحاسبية
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(20) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type ENUM('booking', 'payment', 'receipt', 'adjustment', 'other') DEFAULT 'other',
    reference_id INT NULL,
    total_debit DECIMAL(15,2) NOT NULL,
    total_credit DECIMAL(15,2) NOT NULL,
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
    created_by INT,
    approved_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- جدول أنواع الخدمات
CREATE TABLE service_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category ENUM('flight', 'hotel', 'visa', 'transport', 'hajj', 'umrah', 'package', 'other') NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحجوزات
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_number VARCHAR(20) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    agent_id INT NULL,
    booking_date DATE NOT NULL,
    travel_date DATE,
    return_date DATE NULL,
    service_type_id INT NOT NULL,
    destination VARCHAR(100),
    number_of_passengers INT DEFAULT 1,
    total_amount DECIMAL(15,2) NOT NULL,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    commission_amount DECIMAL(15,2) DEFAULT 0,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    FOREIGN KEY (service_type_id) REFERENCES service_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول تفاصيل الحجوزات
CREATE TABLE booking_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    passenger_name VARCHAR(100) NOT NULL,
    passport_number VARCHAR(20),
    nationality VARCHAR(50),
    date_of_birth DATE,
    gender ENUM('male', 'female'),
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(20) UNIQUE NOT NULL,
    booking_id INT NULL,
    customer_id INT NULL,
    supplier_id INT NULL,
    agent_id INT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'check', 'other') NOT NULL,
    payment_type ENUM('receipt', 'payment') NOT NULL,
    reference_number VARCHAR(50),
    description TEXT,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول المخزون
CREATE TABLE inventory_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_code VARCHAR(20) UNIQUE NOT NULL,
    item_name VARCHAR(100) NOT NULL,
    category ENUM('ticket', 'voucher', 'package', 'service', 'other') DEFAULT 'other',
    description TEXT,
    unit_price DECIMAL(15,2) DEFAULT 0,
    quantity_on_hand INT DEFAULT 0,
    minimum_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- إدراج البيانات الأساسية
INSERT INTO service_types (name, category, description) VALUES
('تذاكر طيران', 'flight', 'حجز تذاكر الطيران الداخلية والدولية'),
('حجز فنادق', 'hotel', 'حجز الفنادق والإقامة'),
('استخراج تأشيرات', 'visa', 'خدمات استخراج التأشيرات'),
('حج', 'hajj', 'خدمات الحج'),
('عمرة', 'umrah', 'خدمات العمرة'),
('باقات سياحية', 'package', 'الباقات السياحية الشاملة');

-- إدراج حسابات أساسية
INSERT INTO chart_of_accounts (account_code, account_name, account_type, level) VALUES
('1000', 'الأصول', 'asset', 1),
('1100', 'الأصول المتداولة', 'asset', 2),
('1110', 'النقدية', 'asset', 3),
('1120', 'العملاء', 'asset', 3),
('2000', 'الخصوم', 'liability', 1),
('2100', 'الخصوم المتداولة', 'liability', 2),
('2110', 'الموردين', 'liability', 3),
('3000', 'حقوق الملكية', 'equity', 1),
('4000', 'الإيرادات', 'revenue', 1),
('4100', 'إيرادات الخدمات', 'revenue', 2),
('5000', 'المصروفات', 'expense', 1),
('5100', 'مصروفات التشغيل', 'expense', 2);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'قيمة الوعد للسفريات', 'string', 'اسم الشركة'),
('company_address', 'الرياض، المملكة العربية السعودية', 'string', 'عنوان الشركة'),
('company_phone', '+966123456789', 'string', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني'),
('default_currency', 'SAR', 'string', 'العملة الافتراضية'),
('tax_rate', '15', 'number', 'معدل الضريبة المضافة');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_suppliers_code ON suppliers(supplier_code);
CREATE INDEX idx_agents_code ON agents(agent_code);
CREATE INDEX idx_bookings_number ON bookings(booking_number);
CREATE INDEX idx_bookings_date ON bookings(booking_date);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_payments_date ON payments(payment_date);
