<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المشتريات - قيمة الوعد للسفر والسياحة</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    
    <!-- ملفات CSS المخصصة -->
    <link href="src/css/purchases.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .system-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .system-subtitle {
            font-size: 1.2rem;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .feature-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .quick-stats {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin: 10px 0;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .action-buttons {
            margin: 30px 0;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .footer-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <div class="mt-3 text-center">
            <h5>جاري تحميل نظام المشتريات...</h5>
        </div>
    </div>

    <!-- الرأس الرئيسي -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="system-title">
                        <i class="fas fa-shopping-cart me-3"></i>
                        نظام المشتريات المتكامل
                    </h1>
                    <p class="system-subtitle">
                        إدارة شاملة للمشتريات والموردين وأوامر الشراء
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="feature-highlight">
                        <div class="feature-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h5>إدارة متقدمة</h5>
                        <p class="mb-0">للموردين والمشتريات</p>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="quick-stats">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-file-invoice text-primary fa-2x"></i>
                            <div class="stat-number" id="quick-total-invoices">0</div>
                            <div class="stat-label">فواتير المشتريات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-truck text-success fa-2x"></i>
                            <div class="stat-number" id="quick-total-suppliers">0</div>
                            <div class="stat-label">الموردين</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-clipboard-list text-info fa-2x"></i>
                            <div class="stat-number" id="quick-total-orders">0</div>
                            <div class="stat-label">أوامر الشراء</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-dollar-sign text-warning fa-2x"></i>
                            <div class="stat-number" id="quick-total-amount">0</div>
                            <div class="stat-label">إجمالي المشتريات</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الإجراءات السريعة -->
            <div class="action-buttons text-center">
                <button class="action-btn" onclick="startPurchasesSystem()">
                    <i class="fas fa-play me-2"></i>
                    بدء استخدام النظام
                </button>
                <button class="action-btn" onclick="showSystemDemo()">
                    <i class="fas fa-eye me-2"></i>
                    عرض توضيحي
                </button>
                <button class="action-btn" onclick="showSystemHelp()">
                    <i class="fas fa-question-circle me-2"></i>
                    المساعدة
                </button>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="footer-info">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-check-circle me-2"></i>الميزات الرئيسية</h6>
                        <ul class="list-unstyled">
                            <li>• إدارة الموردين</li>
                            <li>• فواتير المشتريات</li>
                            <li>• أوامر الشراء</li>
                            <li>• تقارير شاملة</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-globe me-2"></i>دعم العملات</h6>
                        <ul class="list-unstyled">
                            <li>• الريال اليمني (ر.ي)</li>
                            <li>• الريال السعودي (ر.س)</li>
                            <li>• الدولار الأمريكي ($)</li>
                            <li>• تحويل تلقائي</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-shield-alt me-2"></i>الأمان والموثوقية</h6>
                        <ul class="list-unstyled">
                            <li>• حفظ تلقائي للبيانات</li>
                            <li>• نسخ احتياطية</li>
                            <li>• واجهة آمنة</li>
                            <li>• دعم فني متاح</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي للنظام -->
    <main id="purchases-container" style="display: none;">
        <!-- سيتم تحميل نظام المشتريات هنا -->
    </main>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل نظام المشتريات -->
    <script src="src/js/components/purchases.js"></script>
    
    <script>
        // متغيرات عامة
        let systemLoaded = false;
        
        // تحديث الإحصائيات السريعة
        function updateQuickStats() {
            if (typeof window.PurchasesComponent !== 'undefined' && window.PurchasesComponent.data) {
                const data = window.PurchasesComponent.data;
                
                // عدد الفواتير
                const invoicesCount = Object.keys(data.purchaseInvoices || {}).length;
                document.getElementById('quick-total-invoices').textContent = invoicesCount;
                
                // عدد الموردين
                const suppliersCount = Object.keys(data.suppliers || {}).length;
                document.getElementById('quick-total-suppliers').textContent = suppliersCount;
                
                // عدد أوامر الشراء
                const ordersCount = Object.keys(data.purchaseOrders || {}).length;
                document.getElementById('quick-total-orders').textContent = ordersCount;
                
                // إجمالي المشتريات
                const totalAmount = Object.values(data.purchaseInvoices || {}).reduce((sum, invoice) => {
                    return sum + (invoice.total || 0);
                }, 0);
                document.getElementById('quick-total-amount').textContent = totalAmount.toLocaleString('ar-SA');
            }
        }
        
        // بدء استخدام النظام
        function startPurchasesSystem() {
            const loadingScreen = document.getElementById('loading-screen');
            const mainHeader = document.querySelector('.main-header');
            const purchasesContainer = document.getElementById('purchases-container');
            
            // إظهار شاشة التحميل
            loadingScreen.style.display = 'block';
            
            setTimeout(() => {
                try {
                    // تهيئة نظام المشتريات
                    if (typeof window.PurchasesComponent !== 'undefined') {
                        window.PurchasesComponent.init();
                        systemLoaded = true;
                        
                        // إخفاء الرأس وإظهار النظام
                        mainHeader.style.display = 'none';
                        purchasesContainer.style.display = 'block';
                        loadingScreen.style.display = 'none';
                        
                        // تحديث الإحصائيات
                        updateQuickStats();
                        
                        console.log('✅ تم تحميل نظام المشتريات بنجاح');
                        
                    } else {
                        throw new Error('نظام المشتريات غير متاح');
                    }
                } catch (error) {
                    console.error('❌ خطأ في تحميل نظام المشتريات:', error);
                    loadingScreen.style.display = 'none';
                    alert('حدث خطأ في تحميل النظام. يرجى إعادة تحميل الصفحة.');
                }
            }, 1500);
        }
        
        // عرض توضيحي للنظام
        function showSystemDemo() {
            alert('العرض التوضيحي قريباً...\n\nسيتم إضافة جولة تفاعلية لشرح جميع ميزات النظام.');
        }
        
        // إظهار المساعدة
        function showSystemHelp() {
            const helpContent = `
مرحباً بك في نظام المشتريات المتكامل!

الميزات الرئيسية:
• إدارة الموردين والبيانات الخاصة بهم
• إنشاء وإدارة فواتير المشتريات
• إنشاء أوامر الشراء والموافقة عليها
• تتبع المدفوعات والمستحقات
• تقارير شاملة ومفصلة
• دعم العملات المتعددة

للبدء:
1. انقر على "بدء استخدام النظام"
2. استخدم شريط التنقل للانتقال بين الصفحات
3. ابدأ بإضافة الموردين
4. أنشئ أوامر الشراء والفواتير

للمساعدة الفنية:
📧 <EMAIL>
📞 +967 1 234567
            `;
            
            alert(helpContent);
        }
        
        // العودة للصفحة الرئيسية
        function returnToHome() {
            const mainHeader = document.querySelector('.main-header');
            const purchasesContainer = document.getElementById('purchases-container');
            
            mainHeader.style.display = 'block';
            purchasesContainer.style.display = 'none';
            systemLoaded = false;
        }
        
        // إضافة زر العودة للنظام
        window.addEventListener('load', function() {
            // إخفاء شاشة التحميل الأولية
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
            }, 1000);
            
            // تحديث الإحصائيات كل 30 ثانية
            setInterval(updateQuickStats, 30000);
            
            // إضافة معالج للوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // ESC للعودة للصفحة الرئيسية
                if (e.key === 'Escape' && systemLoaded) {
                    if (confirm('هل تريد العودة للصفحة الرئيسية؟')) {
                        returnToHome();
                    }
                }
                
                // F1 للمساعدة
                if (e.key === 'F1') {
                    e.preventDefault();
                    showSystemHelp();
                }
            });
        });
        
        // تحديث الإحصائيات عند تحميل الصفحة
        setTimeout(updateQuickStats, 500);
    </script>
</body>
</html>
