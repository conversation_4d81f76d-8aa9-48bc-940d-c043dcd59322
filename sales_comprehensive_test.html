<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لجميع صفحات المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            padding: 20px;
        }
        .test-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            height: fit-content;
        }
        .test-button {
            width: 100%;
            margin: 8px 0;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
        }
        .test-button.dashboard { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .test-button.invoices { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
        .test-button.customers { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
        .test-button.products { background: linear-gradient(45deg, #ffc107, #e0a800); color: black; }
        .test-button.reports { background: linear-gradient(45deg, #6f42c1, #5a32a3); color: white; }
        .test-button.all { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .test-status {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
        }
        .test-status.success { background: #28a745; }
        .test-status.error { background: #dc3545; }
        .test-status.warning { background: #ffc107; }
        .test-status.testing { background: #17a2b8; animation: pulse 1s infinite; }
        
        .display-area {
            background: white;
            border-radius: 10px;
            border: 2px solid #dee2e6;
            min-height: 600px;
            overflow: auto;
        }
        
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-vial me-3"></i>اختبار شامل لجميع صفحات المبيعات</h1>
            <p class="mb-0">اختبار وتقييم جميع وظائف وصفحات نظام المبيعات</p>
        </div>
        
        <div class="test-grid">
            <!-- لوحة الاختبارات -->
            <div class="test-panel">
                <h4><i class="fas fa-list-check me-2"></i>قائمة الاختبارات</h4>
                
                <button class="test-button dashboard" onclick="testPage('dashboard')">
                    <span class="test-status" id="status-dashboard"></span>
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </button>
                
                <button class="test-button invoices" onclick="testPage('invoices')">
                    <span class="test-status" id="status-invoices"></span>
                    <i class="fas fa-file-invoice me-2"></i>الفواتير
                </button>
                
                <button class="test-button customers" onclick="testPage('customers')">
                    <span class="test-status" id="status-customers"></span>
                    <i class="fas fa-users me-2"></i>العملاء
                </button>
                
                <button class="test-button products" onclick="testPage('products')">
                    <span class="test-status" id="status-products"></span>
                    <i class="fas fa-box me-2"></i>المنتجات
                </button>
                
                <button class="test-button reports" onclick="testPage('reports')">
                    <span class="test-status" id="status-reports"></span>
                    <i class="fas fa-chart-bar me-2"></i>التقارير
                </button>
                
                <hr>
                
                <button class="test-button all" onclick="testAllPages()">
                    <span class="test-status" id="status-all"></span>
                    <i class="fas fa-play me-2"></i>اختبار جميع الصفحات
                </button>
                
                <button class="btn btn-outline-secondary w-100 mt-2" onclick="clearResults()">
                    <i class="fas fa-trash me-2"></i>مسح النتائج
                </button>
                
                <!-- نتائج الاختبار -->
                <div class="test-results">
                    <h6><i class="fas fa-chart-pie me-2"></i>نتائج الاختبار</h6>
                    <div class="metric-card">
                        <strong>الصفحات المختبرة:</strong> <span id="tested-count">0</span>/5
                    </div>
                    <div class="metric-card">
                        <strong>معدل النجاح:</strong> <span id="success-rate">0%</span>
                    </div>
                    <div class="metric-card">
                        <strong>متوسط وقت التحميل:</strong> <span id="avg-load-time">0ms</span>
                    </div>
                    <div class="metric-card">
                        <strong>الأخطاء:</strong> <span id="error-count">0</span>
                    </div>
                </div>
            </div>
            
            <!-- منطقة العرض -->
            <div>
                <div class="display-area" id="main-content">
                    <div class="text-center p-5">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                        <h3 class="text-muted">منطقة عرض صفحات المبيعات</h3>
                        <p class="text-muted">اختر صفحة من القائمة لبدء الاختبار</p>
                    </div>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="log-area" id="test-log">
                    <div class="text-success">[SYSTEM] نظام اختبار المبيعات جاهز</div>
                    <div class="text-info">[INFO] اختر صفحة من القائمة لبدء الاختبار</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // متغيرات الاختبار
        let testResults = {
            dashboard: null,
            invoices: null,
            customers: null,
            products: null,
            reports: null
        };
        
        let testMetrics = {
            testedCount: 0,
            successCount: 0,
            totalLoadTime: 0,
            errorCount: 0
        };

        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateTestStatus(page, status) {
            const statusElement = document.getElementById(`status-${page}`);
            if (statusElement) {
                statusElement.className = `test-status ${status}`;
            }
        }

        // تحديث المقاييس
        function updateMetrics() {
            document.getElementById('tested-count').textContent = testMetrics.testedCount;
            
            const successRate = testMetrics.testedCount > 0 ? 
                Math.round((testMetrics.successCount / testMetrics.testedCount) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            const avgLoadTime = testMetrics.testedCount > 0 ? 
                Math.round(testMetrics.totalLoadTime / testMetrics.testedCount) : 0;
            document.getElementById('avg-load-time').textContent = avgLoadTime + 'ms';
            
            document.getElementById('error-count').textContent = testMetrics.errorCount;
        }

        // اختبار صفحة واحدة
        async function testPage(page) {
            log(`🧪 بدء اختبار صفحة ${getPageName(page)}...`, 'info');
            updateTestStatus(page, 'testing');
            
            const startTime = performance.now();
            let success = false;
            let errorMessage = '';
            
            try {
                // التحقق من وجود المكون
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // اختبار عرض الصفحة
                window.SalesComponent.render({ view: page });
                
                // التحقق من وجود المحتوى
                const content = document.getElementById('main-content');
                if (!content || content.innerHTML.trim().length === 0) {
                    throw new Error('لم يتم إنشاء محتوى للصفحة');
                }
                
                // اختبار إضافي حسب نوع الصفحة
                await performPageSpecificTests(page);
                
                success = true;
                log(`✅ نجح اختبار صفحة ${getPageName(page)}`, 'success');
                updateTestStatus(page, 'success');
                
            } catch (error) {
                errorMessage = error.message;
                log(`❌ فشل اختبار صفحة ${getPageName(page)}: ${errorMessage}`, 'error');
                updateTestStatus(page, 'error');
                testMetrics.errorCount++;
            }
            
            const endTime = performance.now();
            const loadTime = Math.round(endTime - startTime);
            
            // حفظ النتائج
            testResults[page] = {
                success: success,
                loadTime: loadTime,
                error: errorMessage,
                timestamp: new Date().toISOString()
            };
            
            // تحديث المقاييس
            testMetrics.testedCount++;
            if (success) testMetrics.successCount++;
            testMetrics.totalLoadTime += loadTime;
            
            updateMetrics();
            
            log(`📊 وقت التحميل: ${loadTime}ms`, 'info');
        }

        // اختبارات خاصة بكل صفحة
        async function performPageSpecificTests(page) {
            switch (page) {
                case 'dashboard':
                    // اختبار وجود الإحصائيات
                    const stats = document.querySelectorAll('.card');
                    if (stats.length < 4) {
                        throw new Error('بطاقات الإحصائيات غير مكتملة');
                    }
                    break;
                    
                case 'invoices':
                    // اختبار وجود جدول الفواتير
                    const invoiceTable = document.querySelector('table');
                    if (!invoiceTable) {
                        throw new Error('جدول الفواتير غير موجود');
                    }
                    break;
                    
                case 'customers':
                    // اختبار وجود جدول العملاء
                    const customerTable = document.querySelector('table');
                    if (!customerTable) {
                        throw new Error('جدول العملاء غير موجود');
                    }
                    break;
                    
                case 'products':
                    // اختبار وجود جدول المنتجات
                    const productTable = document.querySelector('table');
                    if (!productTable) {
                        throw new Error('جدول المنتجات غير موجود');
                    }
                    break;
                    
                case 'reports':
                    // اختبار وجود التقارير
                    const reportCards = document.querySelectorAll('.card');
                    if (reportCards.length < 2) {
                        throw new Error('بطاقات التقارير غير مكتملة');
                    }
                    break;
            }
            
            // انتظار قصير للتأكد من اكتمال العرض
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        // اختبار جميع الصفحات
        async function testAllPages() {
            log('🚀 بدء اختبار جميع الصفحات...', 'info');
            updateTestStatus('all', 'testing');
            
            const pages = ['dashboard', 'invoices', 'customers', 'products', 'reports'];
            
            for (const page of pages) {
                await testPage(page);
                await new Promise(resolve => setTimeout(resolve, 500)); // انتظار بين الاختبارات
            }
            
            updateTestStatus('all', 'success');
            log('🎉 اكتمل اختبار جميع الصفحات', 'success');
            
            // عرض ملخص النتائج
            showTestSummary();
        }

        // عرض ملخص النتائج
        function showTestSummary() {
            log('📊 ملخص نتائج الاختبار:', 'info');
            
            Object.keys(testResults).forEach(page => {
                const result = testResults[page];
                if (result) {
                    const status = result.success ? '✅' : '❌';
                    log(`${status} ${getPageName(page)}: ${result.loadTime}ms`, 
                        result.success ? 'success' : 'error');
                }
            });
            
            const successRate = Math.round((testMetrics.successCount / testMetrics.testedCount) * 100);
            log(`📈 معدل النجاح الإجمالي: ${successRate}%`, 
                successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'error');
        }

        // الحصول على اسم الصفحة
        function getPageName(page) {
            const names = {
                dashboard: 'لوحة التحكم',
                invoices: 'الفواتير',
                customers: 'العملاء',
                products: 'المنتجات',
                reports: 'التقارير'
            };
            return names[page] || page;
        }

        // مسح النتائج
        function clearResults() {
            testResults = {
                dashboard: null,
                invoices: null,
                customers: null,
                products: null,
                reports: null
            };
            
            testMetrics = {
                testedCount: 0,
                successCount: 0,
                totalLoadTime: 0,
                errorCount: 0
            };
            
            // إعادة تعيين حالات الاختبار
            ['dashboard', 'invoices', 'customers', 'products', 'reports', 'all'].forEach(page => {
                updateTestStatus(page, '');
            });
            
            updateMetrics();
            
            document.getElementById('test-log').innerHTML = `
                <div class="text-success">[SYSTEM] تم مسح جميع النتائج</div>
                <div class="text-info">[INFO] جاهز لاختبارات جديدة</div>
            `;
            
            document.getElementById('main-content').innerHTML = `
                <div class="text-center p-5">
                    <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">منطقة عرض صفحات المبيعات</h3>
                    <p class="text-muted">اختر صفحة من القائمة لبدء الاختبار</p>
                </div>
            `;
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام المبيعات...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('📋 جاهز لبدء الاختبارات', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
