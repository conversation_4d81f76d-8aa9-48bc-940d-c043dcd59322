<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات القوالب - قيمة الوعد للسفريات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .settings-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .settings-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .settings-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .settings-nav {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .nav-tabs {
            border-bottom: none;
            justify-content: center;
        }
        
        .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 600;
            padding: 1rem 2rem;
            margin: 0 0.5rem;
            border-radius: 0;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }
        
        .nav-link.active {
            color: #007bff;
            background: white;
            border-bottom-color: #007bff;
        }
        
        .settings-content {
            padding: 2rem;
        }
        
        .setting-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }
        
        .setting-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
        
        .setting-info {
            flex: 1;
        }
        
        .setting-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        
        .setting-desc {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .setting-control {
            margin-right: 1rem;
        }
        
        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
        }
        
        .template-category {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .category-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .category-name {
            font-weight: 600;
            color: #495057;
        }
        
        .category-count {
            background: #007bff;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .template-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .template-item:last-child {
            border-bottom: none;
        }
        
        .btn-action {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.25rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }
        
        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 600;
        }
        
        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            border: 1px solid #e9ecef;
        }
        
        .backup-info {
            flex: 1;
        }
        
        .backup-name {
            font-weight: 600;
            color: #495057;
        }
        
        .backup-date {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .backup-size {
            font-size: 0.8rem;
            color: #28a745;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .settings-container {
                margin: 1rem;
                border-radius: 15px;
            }
            
            .settings-content {
                padding: 1rem;
            }
            
            .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <!-- رأس الإعدادات -->
        <div class="settings-header">
            <h1 class="settings-title">
                <i class="fas fa-cogs me-2"></i>إعدادات القوالب
            </h1>
            <p class="settings-subtitle">إدارة وتخصيص إعدادات نظام القوالب</p>
        </div>
        
        <!-- شريط التنقل -->
        <div class="settings-nav">
            <ul class="nav nav-tabs" id="settingsTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#general">
                        <i class="fas fa-sliders-h me-1"></i>عام
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#templates">
                        <i class="fas fa-file-alt me-1"></i>القوالب
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#backup">
                        <i class="fas fa-database me-1"></i>النسخ الاحتياطي
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#advanced">
                        <i class="fas fa-code me-1"></i>متقدم
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- محتوى الإعدادات -->
        <div class="settings-content">
            <div class="tab-content">
                <!-- تبويب الإعدادات العامة -->
                <div class="tab-pane fade show active" id="general">
                    <div class="setting-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            الإعدادات العامة
                        </h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">الحفظ التلقائي</div>
                                <div class="setting-desc">حفظ التغييرات تلقائياً أثناء التحرير</div>
                            </div>
                            <div class="setting-control">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">المعاينة المباشرة</div>
                                <div class="setting-desc">عرض المعاينة أثناء التحرير</div>
                            </div>
                            <div class="setting-control">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="livePreview" checked>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">الشبكة المساعدة</div>
                                <div class="setting-desc">عرض الشبكة لمساعدة في التصميم</div>
                            </div>
                            <div class="setting-control">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showGrid">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            إعدادات التصميم
                        </h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">الخط الافتراضي</div>
                                <div class="setting-desc">الخط المستخدم في القوالب الجديدة</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 200px;">
                                    <option selected>Cairo</option>
                                    <option>Amiri</option>
                                    <option>Noto Sans Arabic</option>
                                    <option>Tajawal</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">حجم الخط الافتراضي</div>
                                <div class="setting-desc">حجم الخط للنصوص الجديدة</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-control" value="14" min="8" max="72" style="width: 100px;">
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">اللون الأساسي</div>
                                <div class="setting-desc">اللون الأساسي للقوالب</div>
                            </div>
                            <div class="setting-control">
                                <input type="color" class="form-control form-control-color" value="#007bff" style="width: 60px;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تبويب إدارة القوالب -->
                <div class="tab-pane fade" id="templates">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">24</div>
                            <div class="stat-label">إجمالي القوالب</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">قوالب الفواتير</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">6</div>
                            <div class="stat-label">قوالب التقارير</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">10</div>
                            <div class="stat-label">قوالب أخرى</div>
                        </div>
                    </div>
                    
                    <div class="setting-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            فئات القوالب
                        </h3>
                        
                        <div class="template-category">
                            <div class="category-header">
                                <span class="category-name">قوالب الفواتير</span>
                                <span class="category-count">8</span>
                            </div>
                            <div class="template-list">
                                <div class="template-item">
                                    <span>فاتورة كلاسيكية</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                        <button class="btn btn-sm btn-outline-danger">حذف</button>
                                    </div>
                                </div>
                                <div class="template-item">
                                    <span>فاتورة الحج</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                        <button class="btn btn-sm btn-outline-danger">حذف</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="template-category">
                            <div class="category-header">
                                <span class="category-name">قوالب التقارير</span>
                                <span class="category-count">6</span>
                            </div>
                            <div class="template-list">
                                <div class="template-item">
                                    <span>تقرير المبيعات</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                        <button class="btn btn-sm btn-outline-danger">حذف</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تبويب النسخ الاحتياطي -->
                <div class="tab-pane fade" id="backup">
                    <div class="setting-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            النسخ الاحتياطي
                        </h3>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button class="btn-action btn-success w-100">
                                    <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn-action btn-primary w-100">
                                    <i class="fas fa-upload me-1"></i>استعادة نسخة احتياطية
                                </button>
                            </div>
                        </div>
                        
                        <div class="backup-item">
                            <div class="backup-info">
                                <div class="backup-name">نسخة احتياطية تلقائية</div>
                                <div class="backup-date">15 مارس 2024 - 10:30 ص</div>
                                <div class="backup-size">2.4 ميجابايت</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary">استعادة</button>
                                <button class="btn btn-sm btn-outline-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تبويب الإعدادات المتقدمة -->
                <div class="tab-pane fade" id="advanced">
                    <div class="setting-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            إعدادات متقدمة
                        </h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-name">تصحيح الأخطاء</div>
                                <div class="setting-desc">عرض معلومات تصحيح الأخطاء</div>
                            </div>
                            <div class="setting-control">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="debugMode">
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button class="btn-action btn-danger">
                                <i class="fas fa-trash me-1"></i>إعادة تعيين جميع الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الحفظ -->
            <div class="text-center mt-4">
                <button class="btn-action btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save me-1"></i>حفظ الإعدادات
                </button>
                <button class="btn-action btn-secondary" onclick="window.close()">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
            </div>
        </div>
    </div>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script>
        function saveSettings() {
            alert('✅ تم حفظ الإعدادات بنجاح!');
        }
    </script>
</body>
</html>
