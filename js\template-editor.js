// محرر القوالب المتقدم
window.TemplateEditor = {
    // إعدادات المحرر
    config: {
        version: '1.0.0',
        autoSave: true,
        saveInterval: 30000, // 30 ثانية
        maxUndoSteps: 50,
        gridSize: 10,
        snapToGrid: true
    },

    // حالة المحرر
    state: {
        currentTemplate: null,
        selectedElement: null,
        isDragging: false,
        history: [],
        historyIndex: -1,
        isModified: false,
        activeTab: 'design'
    },

    // العناصر المتاحة
    elements: {
        text: {
            type: 'text',
            name: 'نص',
            icon: 'fas fa-font',
            defaultContent: 'نص تجريبي',
            properties: {
                content: 'نص تجريبي',
                fontSize: '14px',
                fontWeight: 'normal',
                color: '#000000',
                textAlign: 'right',
                padding: '10px',
                margin: '5px'
            }
        },
        heading: {
            type: 'heading',
            name: 'عنو<PERSON>',
            icon: 'fas fa-heading',
            defaultContent: 'عنوان رئيسي',
            properties: {
                content: 'عنوان رئيسي',
                level: 'h2',
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#000000',
                textAlign: 'center',
                padding: '15px',
                margin: '10px'
            }
        },
        table: {
            type: 'table',
            name: 'جدول',
            icon: 'fas fa-table',
            defaultContent: '',
            properties: {
                rows: 3,
                columns: 3,
                borderWidth: '1px',
                borderColor: '#000000',
                cellPadding: '8px',
                width: '100%'
            }
        },
        image: {
            type: 'image',
            name: 'صورة',
            icon: 'fas fa-image',
            defaultContent: '',
            properties: {
                src: '',
                alt: 'صورة',
                width: '200px',
                height: 'auto',
                borderRadius: '0px',
                margin: '10px'
            }
        }
    },

    // تهيئة المحرر
    init: function() {
        console.log('🎨 تهيئة محرر القوالب...');
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.setupTabs();
        this.initCodeEditor();
        this.startAutoSave();
        console.log('✅ تم تهيئة محرر القوالب بنجاح');
    },

    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // تبويبات منطقة العمل
        document.querySelectorAll('.workspace-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(tab.dataset.tab);
            });
        });

        // أزرار شريط الأدوات
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleToolbarAction(btn);
            });
        });

        // حفظ بالاختصار Ctrl+S
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveTemplate();
            }
        });

        // تتبع التغييرات
        document.addEventListener('input', () => {
            this.markAsModified();
        });
    },

    // إعداد السحب والإفلات
    setupDragAndDrop: function() {
        const canvas = document.getElementById('designCanvas');
        const elements = document.querySelectorAll('.element-item');

        // إعداد العناصر القابلة للسحب
        elements.forEach(element => {
            element.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', element.dataset.element);
                this.state.isDragging = true;
            });

            element.addEventListener('dragend', () => {
                this.state.isDragging = false;
            });
        });

        // إعداد منطقة الإفلات
        canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            canvas.classList.add('drag-over');
        });

        canvas.addEventListener('dragleave', () => {
            canvas.classList.remove('drag-over');
        });

        canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            canvas.classList.remove('drag-over');
            
            const elementType = e.dataTransfer.getData('text/plain');
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            this.addElement(elementType, x, y);
        });
    },

    // إعداد التبويبات
    setupTabs: function() {
        this.switchTab('design');
    },

    // تبديل التبويب
    switchTab: function(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.style.display = 'none';
        });

        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.workspace-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // إظهار التبويب المحدد
        const targetTab = document.getElementById(`${tabName}-tab`);
        if (targetTab) {
            targetTab.style.display = 'block';
        }

        // تفعيل الزر المحدد
        const targetButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }

        this.state.activeTab = tabName;

        // تحديث المحتوى حسب التبويب
        if (tabName === 'code') {
            this.updateCodeEditor();
        } else if (tabName === 'preview') {
            this.updatePreview();
        }
    },

    // تهيئة محرر الكود
    initCodeEditor: function() {
        const codeEditor = document.getElementById('codeEditor');
        if (codeEditor) {
            codeEditor.innerHTML = `
                <div style="color: #a0aec0; margin-bottom: 1rem;">
                    <!-- قالب HTML -->
                </div>
                <div style="color: #68d391;">&lt;div class="template-container"&gt;</div>
                <div style="color: #f7fafc; margin-left: 2rem;">
                    <div style="color: #68d391;">&lt;h1&gt;</div>
                    <div style="color: #f7fafc; margin-left: 2rem;">{{company.name}}</div>
                    <div style="color: #68d391;">&lt;/h1&gt;</div>
                </div>
                <div style="color: #68d391;">&lt;/div&gt;</div>
            `;
        }
    },

    // إضافة عنصر جديد
    addElement: function(elementType, x, y) {
        const elementConfig = this.elements[elementType];
        if (!elementConfig) return;

        const canvas = document.getElementById('designCanvas');
        const placeholder = canvas.querySelector('.canvas-placeholder');
        
        // إزالة النص التوضيحي إذا كان موجوداً
        if (placeholder) {
            placeholder.remove();
        }

        // إنشاء العنصر
        const element = document.createElement('div');
        element.className = 'template-element';
        element.dataset.type = elementType;
        element.dataset.id = this.generateId();
        
        // تطبيق الأنماط الأساسية
        element.style.position = 'absolute';
        element.style.left = x + 'px';
        element.style.top = y + 'px';
        element.style.cursor = 'pointer';
        element.style.border = '2px solid transparent';
        element.style.borderRadius = '4px';
        element.style.minWidth = '100px';
        element.style.minHeight = '30px';

        // إضافة المحتوى حسب نوع العنصر
        this.renderElement(element, elementConfig);

        // إضافة مستمعي الأحداث
        this.setupElementEvents(element);

        // إضافة العنصر إلى اللوحة
        canvas.appendChild(element);

        // تحديد العنصر الجديد
        this.selectElement(element);

        // حفظ في التاريخ
        this.saveToHistory();

        console.log(`➕ تم إضافة عنصر ${elementType}`);
    },

    // عرض العنصر
    renderElement: function(element, config) {
        const type = config.type;
        
        switch (type) {
            case 'text':
                element.innerHTML = `<span>${config.defaultContent}</span>`;
                break;
            case 'heading':
                element.innerHTML = `<${config.properties.level}>${config.defaultContent}</${config.properties.level}>`;
                break;
            case 'table':
                element.innerHTML = this.createTable(config.properties);
                break;
            case 'image':
                element.innerHTML = `<img src="https://via.placeholder.com/200x150?text=صورة" alt="${config.properties.alt}" style="max-width: 100%;">`;
                break;
        }
    },

    // إنشاء جدول
    createTable: function(props) {
        let html = '<table style="border-collapse: collapse; width: 100%;">';
        
        for (let i = 0; i < props.rows; i++) {
            html += '<tr>';
            for (let j = 0; j < props.columns; j++) {
                html += `<td style="border: ${props.borderWidth} solid ${props.borderColor}; padding: ${props.cellPadding};">خلية ${i + 1}-${j + 1}</td>`;
            }
            html += '</tr>';
        }
        
        html += '</table>';
        return html;
    },

    // إعداد أحداث العنصر
    setupElementEvents: function(element) {
        // النقر لتحديد العنصر
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });

        // السحب لتحريك العنصر
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        element.addEventListener('mousedown', (e) => {
            if (e.target === element || element.contains(e.target)) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(element.style.left);
                startTop = parseInt(element.style.top);
                element.style.zIndex = '1000';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                element.style.left = (startLeft + deltaX) + 'px';
                element.style.top = (startTop + deltaY) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                element.style.zIndex = 'auto';
                this.saveToHistory();
            }
        });
    },

    // تحديد عنصر
    selectElement: function(element) {
        // إزالة التحديد من العناصر الأخرى
        document.querySelectorAll('.template-element').forEach(el => {
            el.style.border = '2px solid transparent';
        });

        // تحديد العنصر الحالي
        element.style.border = '2px solid #007bff';
        this.state.selectedElement = element;

        // عرض خصائص العنصر
        this.showElementProperties(element);
    },

    // عرض خصائص العنصر
    showElementProperties: function(element) {
        const propertiesPanel = document.getElementById('elementProperties');
        const elementType = element.dataset.type;
        const config = this.elements[elementType];

        if (!config) return;

        let html = `
            <div class="property-item">
                <div class="property-label">نوع العنصر</div>
                <input type="text" class="property-input" value="${config.name}" readonly>
            </div>
        `;

        // خصائص مخصصة حسب نوع العنصر
        Object.entries(config.properties).forEach(([key, value]) => {
            html += `
                <div class="property-item">
                    <div class="property-label">${this.getPropertyLabel(key)}</div>
                    ${this.getPropertyInput(key, value, element)}
                </div>
            `;
        });

        propertiesPanel.innerHTML = html;
    },

    // الحصول على تسمية الخاصية
    getPropertyLabel: function(key) {
        const labels = {
            content: 'المحتوى',
            fontSize: 'حجم الخط',
            fontWeight: 'وزن الخط',
            color: 'اللون',
            textAlign: 'محاذاة النص',
            padding: 'الحشو الداخلي',
            margin: 'الهامش الخارجي',
            width: 'العرض',
            height: 'الارتفاع',
            borderRadius: 'انحناء الحواف',
            rows: 'عدد الصفوف',
            columns: 'عدد الأعمدة',
            borderWidth: 'سمك الحدود',
            borderColor: 'لون الحدود',
            cellPadding: 'حشو الخلايا'
        };
        return labels[key] || key;
    },

    // الحصول على حقل الإدخال للخاصية
    getPropertyInput: function(key, value, element) {
        if (key === 'color' || key === 'borderColor') {
            return `<input type="color" class="color-picker" value="${value}" onchange="TemplateEditor.updateElementProperty('${element.dataset.id}', '${key}', this.value)">`;
        } else if (key === 'textAlign') {
            return `
                <select class="property-input" onchange="TemplateEditor.updateElementProperty('${element.dataset.id}', '${key}', this.value)">
                    <option value="right" ${value === 'right' ? 'selected' : ''}>يمين</option>
                    <option value="center" ${value === 'center' ? 'selected' : ''}>وسط</option>
                    <option value="left" ${value === 'left' ? 'selected' : ''}>يسار</option>
                </select>
            `;
        } else if (key === 'fontWeight') {
            return `
                <select class="property-input" onchange="TemplateEditor.updateElementProperty('${element.dataset.id}', '${key}', this.value)">
                    <option value="normal" ${value === 'normal' ? 'selected' : ''}>عادي</option>
                    <option value="bold" ${value === 'bold' ? 'selected' : ''}>عريض</option>
                </select>
            `;
        } else {
            return `<input type="text" class="property-input" value="${value}" onchange="TemplateEditor.updateElementProperty('${element.dataset.id}', '${key}', this.value)">`;
        }
    },

    // تحديث خاصية العنصر
    updateElementProperty: function(elementId, property, value) {
        const element = document.querySelector(`[data-id="${elementId}"]`);
        if (!element) return;

        // تطبيق التغيير على العنصر
        if (property === 'content') {
            const span = element.querySelector('span');
            if (span) span.textContent = value;
        } else if (property === 'color') {
            element.style.color = value;
        } else if (property === 'fontSize') {
            element.style.fontSize = value;
        } else if (property === 'textAlign') {
            element.style.textAlign = value;
        }
        // يمكن إضافة المزيد من الخصائص هنا

        this.markAsModified();
        this.saveToHistory();
    },

    // توليد معرف فريد
    generateId: function() {
        return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // حفظ في التاريخ
    saveToHistory: function() {
        const canvas = document.getElementById('designCanvas');
        const state = canvas.innerHTML;
        
        this.state.history = this.state.history.slice(0, this.state.historyIndex + 1);
        this.state.history.push(state);
        this.state.historyIndex++;
        
        if (this.state.history.length > this.config.maxUndoSteps) {
            this.state.history.shift();
            this.state.historyIndex--;
        }
    },

    // تحديث محرر الكود
    updateCodeEditor: function() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        console.log('🔧 تحديث محرر الكود...');
    },

    // تحديث المعاينة
    updatePreview: function() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        console.log('👁️ تحديث المعاينة...');
    },

    // وضع علامة التعديل
    markAsModified: function() {
        this.state.isModified = true;
        document.title = '* محرر القوالب المتقدم - قيمة الوعد للسفريات';
    },

    // بدء الحفظ التلقائي
    startAutoSave: function() {
        if (this.config.autoSave) {
            setInterval(() => {
                if (this.state.isModified) {
                    this.autoSave();
                }
            }, this.config.saveInterval);
        }
    },

    // الحفظ التلقائي
    autoSave: function() {
        console.log('💾 حفظ تلقائي...');
        // سيتم تطوير هذه الوظيفة لاحقاً
    }
};

// وظائف الإجراءات
function saveTemplate() {
    console.log('💾 حفظ القالب...');
    TemplateEditor.state.isModified = false;
    document.title = 'محرر القوالب المتقدم - قيمة الوعد للسفريات';
    alert('تم حفظ القالب بنجاح!');
}

function previewTemplate() {
    console.log('👁️ معاينة القالب...');
    TemplateEditor.switchTab('preview');
}

function exportTemplate() {
    console.log('📤 تصدير القالب...');
    alert('سيتم تطوير وظيفة التصدير قريباً...');
}

function closeEditor() {
    if (TemplateEditor.state.isModified) {
        if (confirm('هناك تغييرات غير محفوظة. هل تريد المتابعة؟')) {
            window.close();
        }
    } else {
        window.close();
    }
}

function insertVariable(variable) {
    console.log('📝 إدراج متغير:', variable);
    // سيتم تطوير هذه الوظيفة لاحقاً
    alert(`سيتم إدراج المتغير: ${variable}`);
}

// تهيئة المحرر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    TemplateEditor.init();
});
