/**
 * إصلاح شامل لنظام المبيعات
 * يتضمن تحميل المكون، إصلاح الوظائف، ومعالجة الأخطاء
 */

console.log('🔧 بدء الإصلاح الشامل لنظام المبيعات...');

// متغيرات عامة للتشخيص
let salesSystemStatus = {
    componentLoaded: false,
    initialized: false,
    functionsFixed: false,
    lastError: null,
    diagnostics: {}
};

// وظيفة التشخيص الشامل
function runSalesDiagnostics() {
    console.log('🔍 تشغيل التشخيص الشامل...');

    const diagnostics = {
        timestamp: new Date().toISOString(),
        componentExists: typeof window.SalesComponent !== 'undefined',
        mainContentExists: !!document.getElementById('main-content'),
        requiredFunctions: [],
        missingFunctions: [],
        dataStatus: {},
        errors: []
    };

    // فحص الوظائف المطلوبة
    const requiredFunctions = ['init', 'render', 'renderDashboard', 'loadSalesData', 'saveSalesData', 'diagnose', 'test'];

    if (diagnostics.componentExists) {
        requiredFunctions.forEach(func => {
            if (typeof window.SalesComponent[func] === 'function') {
                diagnostics.requiredFunctions.push(func);
            } else {
                diagnostics.missingFunctions.push(func);
            }
        });

        // فحص البيانات
        try {
            if (window.SalesComponent.data) {
                diagnostics.dataStatus = {
                    hasData: true,
                    customers: Object.keys(window.SalesComponent.data.customers || {}).length,
                    products: Object.keys(window.SalesComponent.data.products || {}).length,
                    invoices: Object.keys(window.SalesComponent.data.invoices || {}).length,
                    settings: !!window.SalesComponent.data.settings
                };
            }
        } catch (error) {
            diagnostics.errors.push('خطأ في فحص البيانات: ' + error.message);
        }
    }

    salesSystemStatus.diagnostics = diagnostics;
    console.log('📊 نتائج التشخيص:', diagnostics);
    return diagnostics;
}

// التحقق من تحميل المكون
if (typeof window.SalesComponent === 'undefined') {
    console.error('❌ مكون المبيعات غير محمل');
    salesSystemStatus.componentLoaded = false;

    // محاولة تحميل المكون يدوياً
    loadSalesComponentManually();
} else {
    console.log('✅ مكون المبيعات محمل مسبقاً');
    salesSystemStatus.componentLoaded = true;
    initSalesComponent();
}

function loadSalesComponentManually() {
    console.log('🔄 محاولة تحميل المكون يدوياً...');

    const script = document.createElement('script');
    script.src = 'src/js/components/sales.js';
    script.onload = function() {
        console.log('✅ تم تحميل مكون المبيعات يدوياً');
        salesSystemStatus.componentLoaded = true;
        initSalesComponent();
    };
    script.onerror = function() {
        console.error('❌ فشل في تحميل مكون المبيعات');
        salesSystemStatus.lastError = 'فشل في تحميل ملف sales.js';
        showErrorNotification('فشل في تحميل نظام المبيعات');
    };
    document.head.appendChild(script);
}

function initSalesComponent() {
    console.log('🔧 تهيئة مكون المبيعات...');

    try {
        // تشغيل التشخيص أولاً
        runSalesDiagnostics();

        // تهيئة المكون
        if (window.SalesComponent && typeof window.SalesComponent.init === 'function') {
            window.SalesComponent.init();
            console.log('✅ تم تهيئة مكون المبيعات');
            salesSystemStatus.initialized = true;
        } else {
            throw new Error('وظيفة init غير متاحة في مكون المبيعات');
        }

        // إصلاح وظائف العرض
        fixSalesFunctions();

        // تشغيل اختبار سريع
        testSalesSystemQuick();

        console.log('🎉 تم إكمال تهيئة نظام المبيعات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة المكون:', error);
        salesSystemStatus.lastError = error.message;
        showErrorNotification('خطأ في تهيئة نظام المبيعات: ' + error.message);
    }
}

function testSalesSystemQuick() {
    console.log('🧪 اختبار سريع للنظام...');

    try {
        if (window.SalesComponent && typeof window.SalesComponent.test === 'function') {
            const result = window.SalesComponent.test();
            if (result) {
                console.log('✅ الاختبار السريع نجح');
                return true;
            } else {
                console.warn('⚠️ الاختبار السريع فشل');
                return false;
            }
        } else {
            console.warn('⚠️ وظيفة الاختبار غير متاحة');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في الاختبار السريع:', error);
        return false;
    }
}

function showErrorNotification(message) {
    // إنشاء إشعار خطأ للمستخدم
    if (typeof alert !== 'undefined') {
        alert('خطأ في نظام المبيعات: ' + message);
    }

    // محاولة إظهار إشعار في الواجهة إذا كان متاحاً
    const container = document.getElementById('main-content');
    if (container) {
        container.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في نظام المبيعات</h4>
                <p>${message}</p>
                <hr>
                <p class="mb-0">يرجى تحديث الصفحة أو الاتصال بالدعم الفني.</p>
            </div>
        `;
    }
}

function fixSalesFunctions() {
    console.log('🔧 إصلاح وظائف المبيعات...');

    // إعادة تعريف وظائف المبيعات مع معالجة أخطاء محسنة
    window.showSalesDashboard = function() {
        console.log('🎯 عرض لوحة تحكم المبيعات...');
        return executeSalesFunction('dashboard', 'لوحة التحكم');
    };

    window.showInvoices = function() {
        console.log('🧾 عرض الفواتير...');
        return executeSalesFunction('invoices', 'الفواتير');
    };

    window.showSalesCustomers = function() {
        console.log('👥 عرض عملاء المبيعات...');
        return executeSalesFunction('customers', 'العملاء');
    };

    window.showSalesProducts = function() {
        console.log('📦 عرض المنتجات...');
        return executeSalesFunction('products', 'المنتجات');
    };

    window.showSalesReports = function() {
        console.log('📊 عرض تقارير المبيعات...');
        return executeSalesFunction('reports', 'التقارير');
    };

    window.showNewInvoice = function() {
        console.log('➕ إنشاء فاتورة جديدة...');
        try {
            if (!validateSalesComponent()) return false;

            if (typeof window.SalesComponent.showCreateInvoiceModal === 'function') {
                window.SalesComponent.showCreateInvoiceModal();
                console.log('✅ تم فتح نافذة الفاتورة الجديدة');
                return true;
            } else {
                // عرض نافذة الفواتير كبديل
                return executeSalesFunction('invoices', 'الفواتير');
            }
        } catch (error) {
            console.error('❌ خطأ في إنشاء فاتورة جديدة:', error);
            showErrorNotification('فشل في فتح نافذة الفاتورة الجديدة');
            return false;
        }
    };

    // وظيفة مساعدة لتنفيذ وظائف المبيعات
    function executeSalesFunction(view, viewName) {
        try {
            if (!validateSalesComponent()) return false;

            const container = document.getElementById('main-content');
            if (!container) {
                console.error('❌ عنصر main-content غير موجود');
                showErrorNotification('خطأ في الواجهة: عنصر العرض الرئيسي غير موجود');
                return false;
            }

            // تهيئة المكون إذا لم يكن مهيئاً
            if (!salesSystemStatus.initialized) {
                window.SalesComponent.init();
                salesSystemStatus.initialized = true;
            }

            window.SalesComponent.render({ view: view });
            console.log(`✅ تم عرض ${viewName} بنجاح`);
            return true;

        } catch (error) {
            console.error(`❌ خطأ في عرض ${viewName}:`, error);
            showErrorNotification(`فشل في عرض ${viewName}: ${error.message}`);
            return false;
        }
    }

    // وظيفة التحقق من صحة مكون المبيعات
    function validateSalesComponent() {
        if (!window.SalesComponent) {
            console.error('❌ مكون المبيعات غير متاح');
            showErrorNotification('مكون المبيعات غير محمل');
            return false;
        }

        if (typeof window.SalesComponent.render !== 'function') {
            console.error('❌ وظيفة العرض غير متاحة');
            showErrorNotification('وظيفة العرض غير متاحة في مكون المبيعات');
            return false;
        }

        return true;
    }

    salesSystemStatus.functionsFixed = true;
    console.log('✅ تم إصلاح جميع وظائف المبيعات');
}

    window.showInvoices = function() {
        console.log('🧾 عرض الفواتير...');
        try {
            if (window.SalesComponent) {
                window.SalesComponent.render({ view: 'invoices' });
                console.log('✅ تم عرض الفواتير بنجاح');
            }
        } catch (error) {
            console.error('❌ خطأ في عرض الفواتير:', error);
        }
    };

    window.showSalesCustomers = function() {
        console.log('👥 عرض عملاء المبيعات...');
        try {
            if (window.SalesComponent) {
                window.SalesComponent.render({ view: 'customers' });
                console.log('✅ تم عرض العملاء بنجاح');
            }
        } catch (error) {
            console.error('❌ خطأ في عرض العملاء:', error);
        }
    };

    window.showSalesProducts = function() {
        console.log('📦 عرض المنتجات...');
        try {
            if (window.SalesComponent) {
                window.SalesComponent.render({ view: 'products' });
                console.log('✅ تم عرض المنتجات بنجاح');
            }
        } catch (error) {
            console.error('❌ خطأ في عرض المنتجات:', error);
        }
    };

// إضافة وظائف إضافية للنظام
window.testSalesSystem = function() {
    console.log('🧪 اختبار نظام المبيعات...');

    const diagnostics = runSalesDiagnostics();

    if (!diagnostics.componentExists) {
        alert('❌ مكون المبيعات غير محمل');
        return false;
    }

    try {
        // اختبار التهيئة
        window.SalesComponent.init();
        console.log('✅ تم تهيئة المكون');

        // اختبار العرض
        window.SalesComponent.render({ view: 'dashboard' });
        console.log('✅ تم عرض لوحة التحكم');

        alert('✅ نظام المبيعات يعمل بشكل صحيح!');
        return true;

    } catch (error) {
        console.error('❌ خطأ في اختبار النظام:', error);
        alert('❌ خطأ في نظام المبيعات: ' + error.message);
        return false;
    }
};

// وظيفة إعادة تحميل النظام
window.reloadSalesSystem = function() {
    console.log('🔄 إعادة تحميل نظام المبيعات...');

    try {
        // إعادة تعيين الحالة
        salesSystemStatus = {
            componentLoaded: false,
            initialized: false,
            functionsFixed: false,
            lastError: null,
            diagnostics: {}
        };

        // إعادة تحميل المكون
        if (window.SalesComponent) {
            window.SalesComponent.init();
            salesSystemStatus.initialized = true;
        }

        // إعادة إصلاح الوظائف
        fixSalesFunctions();

        console.log('✅ تم إعادة تحميل النظام بنجاح');
        alert('تم إعادة تحميل نظام المبيعات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إعادة التحميل:', error);
        alert('فشل في إعادة تحميل النظام: ' + error.message);
    }
};

// وظيفة الحصول على حالة النظام
window.getSalesSystemStatus = function() {
    const diagnostics = runSalesDiagnostics();
    console.log('📊 حالة نظام المبيعات:', salesSystemStatus);
    console.log('📊 تشخيص النظام:', diagnostics);
    return {
        status: salesSystemStatus,
        diagnostics: diagnostics
    };
};

// إضافة معالج أخطاء عام
window.addEventListener('error', function(event) {
    if (event.filename && event.filename.includes('sales.js')) {
        console.error('❌ خطأ في ملف المبيعات:', event.error);
        salesSystemStatus.lastError = event.error.message;
    }
});

console.log('✅ تم إصلاح جميع وظائف المبيعات وإضافة الأدوات المساعدة');
}

// إضافة وظيفة اختبار سريعة
window.testSalesSystem = function() {
    console.log('🧪 اختبار نظام المبيعات...');
    
    if (typeof window.SalesComponent === 'undefined') {
        console.error('❌ مكون المبيعات غير محمل');
        alert('خطأ: مكون المبيعات غير محمل');
        return false;
    }
    
    try {
        // اختبار التهيئة
        window.SalesComponent.init();
        console.log('✅ تم تهيئة المكون');
        
        // اختبار العرض
        window.SalesComponent.render({ view: 'dashboard' });
        console.log('✅ تم عرض لوحة التحكم');
        
        alert('✅ نظام المبيعات يعمل بشكل صحيح!');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام:', error);
        alert('❌ خطأ في نظام المبيعات: ' + error.message);
        return false;
    }
};

console.log('🎉 انتهى إصلاح نظام المبيعات');
