/**
 * تحسينات شاملة لنافذة المبيعات
 * Sales Window Complete Enhancements
 */

console.log('🎨 تحميل تحسينات نافذة المبيعات الشاملة...');

// تحسينات لوحة التحكم
function enhanceDashboard() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة رسوم بيانية تفاعلية
    window.SalesComponent.addInteractiveCharts = function() {
        const chartContainer = document.getElementById('sales-charts');
        if (!chartContainer) return;

        // رسم بياني للمبيعات الشهرية
        const salesChart = `
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-line me-2"></i>المبيعات الشهرية</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlySalesChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        `;

        // رسم بياني للمنتجات الأكثر مبيعاً
        const productsChart = `
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar me-2"></i>المنتجات الأكثر مبيعاً</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="topProductsChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        `;

        chartContainer.innerHTML = salesChart + productsChart;
    };

    // إضافة ويدجت الطقس والوقت
    window.SalesComponent.addTimeWeatherWidget = function() {
        return `
            <div class="col-md-4">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="current-time">${new Date().toLocaleTimeString('ar-SA')}</h4>
                                <p class="mb-0" id="current-date">${new Date().toLocaleDateString('ar-SA')}</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    // إضافة إشعارات ذكية
    window.SalesComponent.addSmartNotifications = function() {
        const notifications = [];
        
        // فحص الفواتير المتأخرة
        const overdueInvoices = Object.values(this.data.invoices || {})
            .filter(inv => inv.status === 'sent' && new Date(inv.dueDate) < new Date());
        
        if (overdueInvoices.length > 0) {
            notifications.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'فواتير متأخرة',
                message: `لديك ${overdueInvoices.length} فاتورة متأخرة الدفع`,
                action: 'showOverdueInvoices'
            });
        }

        // فحص المخزون المنخفض
        const lowStockProducts = Object.values(this.data.products || {})
            .filter(product => product.quantity <= (product.minStock || 5));
        
        if (lowStockProducts.length > 0) {
            notifications.push({
                type: 'info',
                icon: 'fas fa-box',
                title: 'مخزون منخفض',
                message: `${lowStockProducts.length} منتج يحتاج إعادة تخزين`,
                action: 'showLowStockProducts'
            });
        }

        return notifications.map(notif => `
            <div class="alert alert-${notif.type} alert-dismissible fade show" role="alert">
                <i class="${notif.icon} me-2"></i>
                <strong>${notif.title}:</strong> ${notif.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `).join('');
    };

    console.log('✅ تم تحسين لوحة التحكم');
}

// تحسينات واجهة المستخدم
function enhanceUserInterface() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة اختصارات لوحة المفاتيح
    window.SalesComponent.addKeyboardShortcuts = function() {
        document.addEventListener('keydown', function(e) {
            // Ctrl + N = فاتورة جديدة
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                window.SalesComponent.showCreateInvoiceModal();
            }
            
            // Ctrl + Shift + C = عميل جديد
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                window.SalesComponent.showCreateCustomerModal();
            }
            
            // Ctrl + Shift + P = منتج جديد
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                window.SalesComponent.showCreateProductModal();
            }
            
            // F1 = مساعدة
            if (e.key === 'F1') {
                e.preventDefault();
                window.SalesComponent.showHelpModal();
            }
        });
    };

    // إضافة نظام السحب والإفلات
    window.SalesComponent.addDragAndDrop = function() {
        // السحب والإفلات للمنتجات في الفواتير
        const productSelects = document.querySelectorAll('.product-select');
        productSelects.forEach(select => {
            select.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('drag-over');
            });
            
            select.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });
            
            select.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                // معالجة الإفلات
            });
        });
    };

    // إضافة نظام البحث الذكي
    window.SalesComponent.addSmartSearch = function() {
        const searchInput = document.getElementById('global-search');
        if (!searchInput) return;

        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            if (query.length < 2) return;

            // البحث في العملاء والمنتجات والفواتير
            const results = {
                customers: Object.values(window.SalesComponent.data.customers || {})
                    .filter(c => c.name.toLowerCase().includes(query)),
                products: Object.values(window.SalesComponent.data.products || {})
                    .filter(p => p.name.toLowerCase().includes(query)),
                invoices: Object.values(window.SalesComponent.data.invoices || {})
                    .filter(i => i.number.toLowerCase().includes(query))
            };

            // عرض النتائج
            window.SalesComponent.showSearchResults(results);
        });
    };

    // إضافة نظام الإشعارات المنبثقة
    window.SalesComponent.addToastNotifications = function() {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);

        this.showToast = function(message, type = 'info', duration = 3000) {
            const toastId = 'toast-' + Date.now();
            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast, { delay: duration });
            bsToast.show();
            
            // إزالة التوست بعد الإخفاء
            toast.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        };
    };

    console.log('✅ تم تحسين واجهة المستخدم');
}

// تحسينات الأداء
function enhancePerformance() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة نظام التخزين المؤقت
    window.SalesComponent.cache = {
        data: new Map(),
        views: new Map(),
        
        set: function(key, value, ttl = 300000) { // 5 دقائق افتراضي
            this.data.set(key, {
                value: value,
                expires: Date.now() + ttl
            });
        },
        
        get: function(key) {
            const item = this.data.get(key);
            if (!item) return null;
            
            if (Date.now() > item.expires) {
                this.data.delete(key);
                return null;
            }
            
            return item.value;
        },
        
        clear: function() {
            this.data.clear();
            this.views.clear();
        }
    };

    // تحسين تحميل البيانات
    const originalLoadSalesData = window.SalesComponent.loadSalesData;
    window.SalesComponent.loadSalesData = function() {
        const cached = this.cache.get('salesData');
        if (cached) {
            this.data = cached;
            return;
        }
        
        originalLoadSalesData.call(this);
        this.cache.set('salesData', this.data);
    };

    // تحسين عرض القوائم الطويلة
    window.SalesComponent.addVirtualScrolling = function(containerId, items, renderItem) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const itemHeight = 50; // ارتفاع العنصر الواحد
        const visibleItems = Math.ceil(container.clientHeight / itemHeight);
        const totalHeight = items.length * itemHeight;

        let scrollTop = 0;
        let startIndex = 0;
        let endIndex = Math.min(startIndex + visibleItems + 5, items.length);

        function updateView() {
            const visibleItems = items.slice(startIndex, endIndex);
            container.innerHTML = visibleItems.map(renderItem).join('');
            container.style.paddingTop = startIndex * itemHeight + 'px';
            container.style.paddingBottom = (items.length - endIndex) * itemHeight + 'px';
        }

        container.addEventListener('scroll', function() {
            scrollTop = this.scrollTop;
            startIndex = Math.floor(scrollTop / itemHeight);
            endIndex = Math.min(startIndex + visibleItems + 5, items.length);
            updateView();
        });

        updateView();
    };

    console.log('✅ تم تحسين الأداء');
}

// تحسينات الأمان
function enhanceSecurity() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة تشفير البيانات الحساسة
    window.SalesComponent.encrypt = function(data) {
        // تشفير بسيط (في الإنتاج يجب استخدام مكتبة تشفير قوية)
        return btoa(JSON.stringify(data));
    };

    window.SalesComponent.decrypt = function(encryptedData) {
        try {
            return JSON.parse(atob(encryptedData));
        } catch (error) {
            console.error('فشل في فك التشفير:', error);
            return null;
        }
    };

    // إضافة تسجيل العمليات
    window.SalesComponent.auditLog = [];
    
    window.SalesComponent.logAction = function(action, details) {
        this.auditLog.push({
            timestamp: new Date().toISOString(),
            action: action,
            details: details,
            user: 'current_user' // في الإنتاج يجب الحصول على المستخدم الحالي
        });
        
        // الاحتفاظ بآخر 1000 عملية فقط
        if (this.auditLog.length > 1000) {
            this.auditLog = this.auditLog.slice(-1000);
        }
    };

    // إضافة التحقق من صحة البيانات المتقدم
    window.SalesComponent.validateData = function(data, schema) {
        const errors = [];
        
        for (const field in schema) {
            const rules = schema[field];
            const value = data[field];
            
            if (rules.required && (!value || value === '')) {
                errors.push(`الحقل ${field} مطلوب`);
            }
            
            if (rules.type && value && typeof value !== rules.type) {
                errors.push(`الحقل ${field} يجب أن يكون من نوع ${rules.type}`);
            }
            
            if (rules.minLength && value && value.length < rules.minLength) {
                errors.push(`الحقل ${field} يجب أن يكون ${rules.minLength} أحرف على الأقل`);
            }
            
            if (rules.pattern && value && !rules.pattern.test(value)) {
                errors.push(`الحقل ${field} لا يطابق النمط المطلوب`);
            }
        }
        
        return errors;
    };

    console.log('✅ تم تحسين الأمان');
}

// تحسينات إضافية
function enhanceAdditionalFeatures() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة نظام النسخ الاحتياطي التلقائي
    window.SalesComponent.autoBackup = function() {
        setInterval(() => {
            const backup = {
                timestamp: new Date().toISOString(),
                data: this.data
            };
            
            localStorage.setItem('sales_backup_' + Date.now(), JSON.stringify(backup));
            
            // الاحتفاظ بآخر 5 نسخ احتياطية فقط
            const backups = Object.keys(localStorage)
                .filter(key => key.startsWith('sales_backup_'))
                .sort()
                .reverse();
            
            if (backups.length > 5) {
                backups.slice(5).forEach(key => localStorage.removeItem(key));
            }
        }, 300000); // كل 5 دقائق
    };

    // إضافة نظام التصدير المتقدم
    window.SalesComponent.exportToExcel = function(data, filename) {
        // تحويل البيانات إلى CSV
        const csv = this.convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename + '.csv';
        link.click();
    };

    window.SalesComponent.convertToCSV = function(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => 
                JSON.stringify(row[header] || '')
            ).join(','))
        ].join('\n');
        
        return csvContent;
    };

    // إضافة نظام الطباعة المتقدم
    window.SalesComponent.advancedPrint = function(content, options = {}) {
        const printWindow = window.open('', '_blank');
        const styles = options.styles || '';
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${options.title || 'طباعة'}</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    @media print { 
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                    ${styles}
                </style>
            </head>
            <body>
                ${content}
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    };

    console.log('✅ تم إضافة الميزات الإضافية');
}

// تطبيق جميع التحسينات
function applyAllEnhancements() {
    console.log('🚀 تطبيق جميع تحسينات نافذة المبيعات...');
    
    try {
        enhanceDashboard();
        enhanceUserInterface();
        enhancePerformance();
        enhanceSecurity();
        enhanceAdditionalFeatures();
        
        console.log('✅ تم تطبيق جميع التحسينات بنجاح');
        
        // إشعار المستخدم
        if (typeof window.SalesComponent !== 'undefined' && window.SalesComponent.showToast) {
            window.SalesComponent.showToast('تم تطبيق جميع التحسينات على نافذة المبيعات', 'success');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تطبيق التحسينات:', error);
    }
}

// تشغيل التحسينات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyAllEnhancements);
} else {
    applyAllEnhancements();
}

// تصدير الوظائف للاستخدام الخارجي
window.SalesEnhancements = {
    enhanceDashboard,
    enhanceUserInterface,
    enhancePerformance,
    enhanceSecurity,
    enhanceAdditionalFeatures,
    applyAllEnhancements
};

console.log('🎉 تم تحميل جميع تحسينات نافذة المبيعات');
