/**
 * تحسينات ملخص الفواتير مع ربط العملات والتحكم في الضرائب
 * Invoice Summary Enhancements with Currency Linking and Tax Control
 */

console.log('🧾 تحميل تحسينات ملخص الفواتير...');

// تحسين وظيفة حساب إجماليات الفاتورة
function enhanceInvoiceSummary() {
    if (typeof window.SalesComponent === 'undefined') return;

    // إضافة متغيرات للتحكم في الضريبة والعملة
    window.SalesComponent.invoiceSettings = {
        showTax: true,
        defaultCurrency: 'YER',
        currentInvoiceCurrency: 'YER',
        taxEnabled: true,
        taxRate: 0.15
    };

    // وظيفة محسنة لحساب إجماليات الفاتورة مع دعم العملات المتعددة
    window.SalesComponent.calculateInvoiceTotalEnhanced = function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return { subtotal: 0, tax: 0, total: 0, itemCount: 0 };

        let subtotalByCurrency = {};
        let itemCount = 0;
        let mainCurrency = this.invoiceSettings.currentInvoiceCurrency;

        // حساب المجموع الفرعي لكل عملة
        Array.from(itemsContainer.children).forEach(item => {
            const productSelect = item.querySelector('.product-select');
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (productSelect && quantityInput && priceInput && totalInput) {
                const productId = productSelect.value;
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const total = quantity * price;

                if (productId && quantity > 0 && price > 0) {
                    // الحصول على عملة المنتج
                    const product = this.data.products[productId];
                    const productCurrency = product?.currency || 'YER';

                    // تحويل إلى العملة الرئيسية للفاتورة
                    const convertedTotal = this.convertCurrency(total, productCurrency, mainCurrency);

                    if (!subtotalByCurrency[mainCurrency]) {
                        subtotalByCurrency[mainCurrency] = 0;
                    }
                    subtotalByCurrency[mainCurrency] += convertedTotal;

                    // تحديث إجمالي العنصر بالعملة المحولة
                    totalInput.value = convertedTotal.toFixed(2);
                    
                    // تحديث عرض العملة للعنصر
                    const currencySpan = item.querySelector('.item-currency');
                    if (currencySpan) {
                        currencySpan.textContent = this.getCurrencySymbol(mainCurrency);
                    }

                    itemCount++;
                }
            }
        });

        const subtotal = subtotalByCurrency[mainCurrency] || 0;

        // حساب الخصم
        const discountElement = document.getElementById('discountAmount');
        const discount = discountElement ? parseFloat(discountElement.value) || 0 : 0;
        const validDiscount = Math.min(discount, subtotal);

        // حساب المجموع بعد الخصم
        const subtotalAfterDiscount = subtotal - validDiscount;

        // حساب الضريبة (إذا كانت مفعلة)
        let tax = 0;
        if (this.invoiceSettings.taxEnabled && this.invoiceSettings.showTax) {
            tax = subtotalAfterDiscount * this.invoiceSettings.taxRate;
        }

        // حساب الإجمالي النهائي
        const total = subtotalAfterDiscount + tax;

        // تحديث عناصر العرض
        this.updateInvoiceSummaryDisplay(subtotal, tax, total, itemCount, mainCurrency);

        return {
            subtotal: subtotal,
            tax: tax,
            total: total,
            itemCount: itemCount,
            currency: mainCurrency
        };
    };

    // تحديث عرض ملخص الفاتورة
    window.SalesComponent.updateInvoiceSummaryDisplay = function(subtotal, tax, total, itemCount, currency) {
        const currencySymbol = this.getCurrencySymbol(currency);

        // تحديث عدد العناصر
        const itemCountElement = document.getElementById('invoiceItemCount');
        if (itemCountElement) {
            itemCountElement.textContent = itemCount;
            itemCountElement.className = itemCount > 0 ? 'badge bg-success' : 'badge bg-secondary';
        }

        // تحديث المجموع الفرعي
        const subtotalElement = document.getElementById('invoiceSubtotal');
        if (subtotalElement) {
            subtotalElement.textContent = `${subtotal.toFixed(2)} ${currencySymbol}`;
            this.animateValue(subtotalElement);
        }

        // تحديث الضريبة
        const taxElement = document.getElementById('invoiceTax');
        if (taxElement) {
            if (this.invoiceSettings.showTax && this.invoiceSettings.taxEnabled) {
                taxElement.textContent = `${tax.toFixed(2)} ${currencySymbol}`;
                taxElement.parentElement.style.display = 'flex';
            } else {
                taxElement.parentElement.style.display = 'none';
            }
            this.animateValue(taxElement);
        }

        // تحديث الإجمالي
        const totalElement = document.getElementById('invoiceTotal');
        if (totalElement) {
            totalElement.textContent = `${total.toFixed(2)} ${currencySymbol}`;
            this.animateValue(totalElement);

            // تغيير لون الإجمالي حسب القيمة
            if (total > 1000) {
                totalElement.className = 'h5 text-success fw-bold';
            } else if (total > 500) {
                totalElement.className = 'h5 text-primary fw-bold';
            } else {
                totalElement.className = 'h5 text-info fw-bold';
            }
        }

        // تحديث رمز العملة في حقل الخصم
        const discountCurrencySpan = document.querySelector('#discountAmount + .input-group-text');
        if (discountCurrencySpan) {
            discountCurrencySpan.textContent = currencySymbol;
        }
    };

    // إضافة وظيفة تغيير عملة الفاتورة
    window.SalesComponent.changeInvoiceCurrency = function(newCurrency) {
        this.invoiceSettings.currentInvoiceCurrency = newCurrency;
        this.calculateInvoiceTotalEnhanced();
        this.showNotification(`تم تغيير عملة الفاتورة إلى ${this.getCurrencyName(newCurrency)}`, 'success');
    };

    // إضافة وظيفة تفعيل/إلغاء الضريبة
    window.SalesComponent.toggleTax = function(enabled) {
        this.invoiceSettings.taxEnabled = enabled;
        this.calculateInvoiceTotalEnhanced();
        
        const message = enabled ? 'تم تفعيل الضريبة' : 'تم إلغاء الضريبة';
        this.showNotification(message, 'info');
    };

    // إضافة وظيفة إظهار/إخفاء الضريبة
    window.SalesComponent.toggleTaxDisplay = function(show) {
        this.invoiceSettings.showTax = show;
        this.calculateInvoiceTotalEnhanced();
        
        const message = show ? 'تم إظهار الضريبة في الملخص' : 'تم إخفاء الضريبة من الملخص';
        this.showNotification(message, 'info');
    };

    // إضافة وظيفة تحديث عملة المنتج في الفاتورة
    window.SalesComponent.updateProductCurrencyInInvoice = function(itemElement, productId) {
        const product = this.data.products[productId];
        if (!product) return;

        const productCurrency = product.currency || 'YER';
        const priceInput = itemElement.querySelector('.item-price');
        const currencySpan = itemElement.querySelector('.product-currency');

        if (priceInput) {
            priceInput.value = product.price || 0;
        }

        if (currencySpan) {
            currencySpan.textContent = this.getCurrencySymbol(productCurrency);
            currencySpan.setAttribute('data-currency', productCurrency);
        }

        // إعادة حساب الإجماليات
        this.calculateInvoiceTotalEnhanced();
    };

    console.log('✅ تم تحسين ملخص الفواتير');
}

// تحسين واجهة ملخص الفاتورة
function enhanceInvoiceSummaryUI() {
    if (typeof window.SalesComponent === 'undefined') return;

    // تحسين عرض ملخص الفاتورة في نموذج إنشاء الفاتورة
    const originalShowCreateInvoiceModal = window.SalesComponent.showCreateInvoiceModal;
    window.SalesComponent.showCreateInvoiceModal = function() {
        const result = originalShowCreateInvoiceModal.call(this);
        
        // إضافة عناصر التحكم المحسنة لملخص الفاتورة
        setTimeout(() => {
            this.addEnhancedInvoiceSummaryControls();
        }, 100);
        
        return result;
    };

    // إضافة عناصر التحكم المحسنة
    window.SalesComponent.addEnhancedInvoiceSummaryControls = function() {
        const summaryCard = document.querySelector('.invoice-summary');
        if (!summaryCard) return;

        // إضافة اختيار العملة
        const currencyControlHTML = `
            <div class="d-flex justify-content-between mb-3">
                <span><i class="fas fa-coins me-1"></i>عملة الفاتورة:</span>
                <select class="form-select form-select-sm" id="invoiceCurrencySelect" style="width: 120px;" onchange="window.SalesComponent.changeInvoiceCurrency(this.value)">
                    <option value="YER">ريال يمني</option>
                    <option value="SAR">ريال سعودي</option>
                    <option value="USD">دولار أمريكي</option>
                </select>
            </div>
            <hr>
        `;

        // إضافة التحكم في الضريبة
        const taxControlHTML = `
            <div class="tax-controls mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><i class="fas fa-percentage me-1"></i>إعدادات الضريبة:</span>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="checkbox" class="btn-check" id="taxEnabledCheck" ${this.invoiceSettings.taxEnabled ? 'checked' : ''} onchange="window.SalesComponent.toggleTax(this.checked)">
                        <label class="btn btn-outline-primary" for="taxEnabledCheck">
                            <i class="fas fa-calculator"></i>
                        </label>
                        
                        <input type="checkbox" class="btn-check" id="taxDisplayCheck" ${this.invoiceSettings.showTax ? 'checked' : ''} onchange="window.SalesComponent.toggleTaxDisplay(this.checked)">
                        <label class="btn btn-outline-info" for="taxDisplayCheck">
                            <i class="fas fa-eye"></i>
                        </label>
                    </div>
                </div>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    الأول: تفعيل الضريبة | الثاني: إظهار في الملخص
                </small>
            </div>
            <hr>
        `;

        // إدراج العناصر الجديدة في بداية ملخص الفاتورة
        const firstElement = summaryCard.firstElementChild;
        if (firstElement) {
            firstElement.insertAdjacentHTML('beforebegin', currencyControlHTML + taxControlHTML);
        }
    };

    // تحسين عرض عناصر الفاتورة مع العملات
    const originalAddInvoiceItem = window.SalesComponent.addInvoiceItem;
    window.SalesComponent.addInvoiceItem = function() {
        const result = originalAddInvoiceItem.call(this);
        
        // إضافة معالج تغيير المنتج لتحديث العملة
        setTimeout(() => {
            const lastItem = document.querySelector('#invoiceItems .invoice-item:last-child');
            if (lastItem) {
                const productSelect = lastItem.querySelector('.product-select');
                if (productSelect) {
                    productSelect.addEventListener('change', (e) => {
                        this.updateProductCurrencyInInvoice(lastItem, e.target.value);
                    });
                }
            }
        }, 100);
        
        return result;
    };

    console.log('✅ تم تحسين واجهة ملخص الفاتورة');
}

// تحسين عرض العملات في عناصر الفاتورة
function enhanceInvoiceItemsCurrency() {
    if (typeof window.SalesComponent === 'undefined') return;

    // تحسين عرض عنصر الفاتورة مع العملة
    const originalRenderInvoiceItem = window.SalesComponent.renderInvoiceItem;
    window.SalesComponent.renderInvoiceItem = function(index) {
        const itemHTML = `
            <div class="invoice-item border rounded p-3 mb-3" data-index="${index}">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label class="form-label">المنتج</label>
                        <select class="form-control product-select" onchange="window.SalesComponent.updateProductCurrencyInInvoice(this.closest('.invoice-item'), this.value); window.SalesComponent.calculateInvoiceTotalEnhanced();">
                            <option value="">اختر المنتج</option>
                            ${Object.values(this.data.products || {}).map(product => `
                                <option value="${product.id}" data-price="${product.price}" data-currency="${product.currency || 'YER'}">
                                    ${product.name} - ${this.formatAmount(product.price, product.currency)}
                                </option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الكمية</label>
                        <input type="number" class="form-control item-quantity" min="1" value="1" onchange="window.SalesComponent.calculateItemTotal(this); window.SalesComponent.calculateInvoiceTotalEnhanced();">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">السعر</label>
                        <div class="input-group">
                            <input type="number" class="form-control item-price" min="0" step="0.01" onchange="window.SalesComponent.calculateItemTotal(this); window.SalesComponent.calculateInvoiceTotalEnhanced();">
                            <span class="input-group-text product-currency">ر.ي</span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الإجمالي</label>
                        <div class="input-group">
                            <input type="number" class="form-control item-total" readonly>
                            <span class="input-group-text item-currency">ر.ي</span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">العملة</label>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info currency-badge" data-currency="YER">ريال يمني</span>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-sm d-block" onclick="window.SalesComponent.removeInvoiceItem(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return itemHTML;
    };

    console.log('✅ تم تحسين عرض العملات في عناصر الفاتورة');
}

// إضافة أنماط CSS للتحسينات
function addInvoiceSummaryStyles() {
    const styles = `
        <style id="invoice-summary-enhancements">
            .tax-controls {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 8px;
                padding: 12px;
                border: 1px solid #dee2e6;
            }
            
            .currency-badge {
                font-size: 0.75rem;
                padding: 4px 8px;
                border-radius: 12px;
            }
            
            .product-currency, .item-currency {
                background: linear-gradient(45deg, #007bff, #0056b3);
                color: white;
                font-weight: 600;
                min-width: 50px;
                text-align: center;
            }
            
            #invoiceCurrencySelect {
                border: 2px solid #007bff;
                border-radius: 8px;
                font-weight: 600;
            }
            
            .invoice-summary {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                border: 2px solid #e9ecef;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            
            .btn-check:checked + .btn-outline-primary {
                background: linear-gradient(45deg, #007bff, #0056b3);
                border-color: #007bff;
            }
            
            .btn-check:checked + .btn-outline-info {
                background: linear-gradient(45deg, #17a2b8, #138496);
                border-color: #17a2b8;
            }
            
            .invoice-item {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                border: 2px solid #e9ecef !important;
                transition: all 0.3s ease;
            }
            
            .invoice-item:hover {
                border-color: #007bff !important;
                box-shadow: 0 4px 15px rgba(0,123,255,0.1);
                transform: translateY(-2px);
            }
        </style>
    `;
    
    if (!document.getElementById('invoice-summary-enhancements')) {
        document.head.insertAdjacentHTML('beforeend', styles);
    }
}

// تطبيق جميع التحسينات
function applyInvoiceSummaryEnhancements() {
    console.log('🚀 تطبيق تحسينات ملخص الفواتير...');
    
    try {
        enhanceInvoiceSummary();
        enhanceInvoiceSummaryUI();
        enhanceInvoiceItemsCurrency();
        addInvoiceSummaryStyles();
        
        console.log('✅ تم تطبيق جميع تحسينات ملخص الفواتير بنجاح');
        
        // إشعار المستخدم
        if (typeof window.SalesComponent !== 'undefined' && window.SalesComponent.showToast) {
            window.SalesComponent.showToast('تم تطبيق تحسينات ملخص الفواتير مع ربط العملات والتحكم في الضرائب', 'success');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تطبيق تحسينات ملخص الفواتير:', error);
    }
}

// تشغيل التحسينات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyInvoiceSummaryEnhancements);
} else {
    applyInvoiceSummaryEnhancements();
}

// تصدير الوظائف للاستخدام الخارجي
window.InvoiceSummaryEnhancements = {
    enhanceInvoiceSummary,
    enhanceInvoiceSummaryUI,
    enhanceInvoiceItemsCurrency,
    addInvoiceSummaryStyles,
    applyInvoiceSummaryEnhancements
};

console.log('🎉 تم تحميل جميع تحسينات ملخص الفواتير');
