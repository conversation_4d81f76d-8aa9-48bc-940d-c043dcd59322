const fs = require('fs');

// قراءة الملف
let content = fs.readFileSync('./src/js/components/accounting.js', 'utf8');

// عد الاستدعاءات قبل الإصلاح
const beforeCount = (content.match(/onclick="AccountingComponent\./g) || []).length;
console.log(`عدد الاستدعاءات قبل الإصلاح: ${beforeCount}`);

// إصلاح جميع الاستدعاءات
content = content.replace(/onclick="AccountingComponent\./g, 'onclick="window.AccountingComponent.');
content = content.replace(/onchange="AccountingComponent\./g, 'onchange="window.AccountingComponent.');

// عد الاستدعاءات بعد الإصلاح
const afterCount = (content.match(/onclick="AccountingComponent\./g) || []).length;
const fixedCount = (content.match(/onclick="window\.AccountingComponent\./g) || []).length;

// كتابة الملف المحدث
fs.writeFileSync('./src/js/components/accounting.js', content);

console.log(`عدد الاستدعاءات بعد الإصلاح: ${afterCount}`);
console.log(`عدد الاستدعاءات المصححة: ${fixedCount}`);

if (afterCount === 0) {
    console.log('✅ تم إصلاح جميع الاستدعاءات بنجاح!');
} else {
    console.log(`⚠️ لا يزال هناك ${afterCount} استدعاء يحتاج إصلاح`);
}
