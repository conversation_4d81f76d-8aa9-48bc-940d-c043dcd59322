<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة القوالب - قيمة الوعد للسفريات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .templates-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .system-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .system-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .system-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }
        
        .system-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 2;
        }
        
        .templates-nav {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
        }
        
        .nav-pills .nav-link {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.25rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .nav-pills .nav-link:hover:not(.active) {
            background-color: #e9ecef;
            transform: translateY(-1px);
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .template-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }
        
        .template-card:hover::before {
            transform: scaleX(1);
        }
        
        .template-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .template-card h5 {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }
        
        .template-card p {
            color: #6c757d;
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .template-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-template {
            flex: 1;
            min-width: 100px;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .template-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
        }
        
        .stat-number {
            font-weight: 700;
            font-size: 1.2rem;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
        
        .fab {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        @media (max-width: 768px) {
            .templates-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .system-header h1 {
                font-size: 2rem;
            }
            
            .templates-container {
                padding: 1rem;
                border-radius: 15px;
            }
            
            .floating-actions {
                bottom: 20px;
                right: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="templates-container">
        <!-- رأس النظام -->
        <div class="system-header">
            <h1><i class="fas fa-file-alt me-3"></i>نظام إدارة القوالب</h1>
            <p>إدارة شاملة ومتطورة لجميع قوالب النظام المحاسبي للسفريات</p>
        </div>
        
        <!-- شريط البحث -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-8">
                    <input type="text" class="form-control search-input" id="templateSearch" 
                           placeholder="🔍 البحث في القوالب...">
                </div>
                <div class="col-md-4">
                    <select class="form-select search-input" id="categoryFilter">
                        <option value="">جميع الفئات</option>
                        <option value="invoices">قوالب الفواتير</option>
                        <option value="reports">قوالب التقارير</option>
                        <option value="documents">قوالب الوثائق</option>
                        <option value="contracts">قوالب العقود</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- تبويبات التصنيف -->
        <div class="templates-nav">
            <ul class="nav nav-pills justify-content-center" id="templatesNav">
                <li class="nav-item">
                    <a class="nav-link active" data-category="all" href="#all">
                        <i class="fas fa-th-large me-2"></i>جميع القوالب
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-category="invoices" href="#invoices">
                        <i class="fas fa-file-invoice me-2"></i>قوالب الفواتير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-category="reports" href="#reports">
                        <i class="fas fa-chart-bar me-2"></i>قوالب التقارير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-category="documents" href="#documents">
                        <i class="fas fa-file-alt me-2"></i>قوالب الوثائق
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-category="contracts" href="#contracts">
                        <i class="fas fa-handshake me-2"></i>قوالب العقود
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- شبكة القوالب -->
        <div class="templates-grid" id="templatesGrid">
            <!-- سيتم ملء القوالب هنا بواسطة JavaScript -->
        </div>
    </div>
    
    <!-- أزرار الإجراءات العائمة -->
    <div class="floating-actions">
        <button class="fab" onclick="createNewTemplate()" title="إنشاء قالب جديد">
            <i class="fas fa-plus"></i>
        </button>
        <button class="fab" onclick="importTemplate()" title="استيراد قالب">
            <i class="fas fa-upload"></i>
        </button>
        <button class="fab" onclick="showTemplateSettings()" title="إعدادات القوالب">
            <i class="fas fa-cog"></i>
        </button>
    </div>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/templates-system.js"></script>
</body>
</html>
