// نظام إدارة القوالب المتطور
window.TemplatesSystem = {
    // إعدادات النظام
    config: {
        version: '1.0.0',
        maxTemplateSize: 5 * 1024 * 1024, // 5MB
        supportedFormats: ['html', 'pdf', 'docx', 'xlsx'],
        autoSave: true,
        backupInterval: 300000 // 5 دقائق
    },

    // بيانات القوالب الافتراضية
    defaultTemplates: {
        invoices: [
            {
                id: 'inv_classic',
                name: 'فاتورة كلاسيكية',
                description: 'قالب فاتورة تقليدي أنيق للاستخدام العام',
                category: 'invoices',
                type: 'invoice',
                icon: 'fas fa-file-invoice',
                color: '#007bff',
                usage: 245,
                lastModified: new Date().toISOString(),
                isDefault: true,
                template: {
                    header: {
                        companyName: '{{company.name}}',
                        companyAddress: '{{company.address}}',
                        companyPhone: '{{company.phone}}',
                        companyEmail: '{{company.email}}',
                        logo: '{{company.logo}}'
                    },
                    invoice: {
                        number: '{{invoice.number}}',
                        date: '{{invoice.date}}',
                        dueDate: '{{invoice.dueDate}}',
                        currency: '{{invoice.currency}}'
                    },
                    customer: {
                        name: '{{customer.name}}',
                        address: '{{customer.address}}',
                        phone: '{{customer.phone}}',
                        email: '{{customer.email}}'
                    },
                    items: '{{invoice.items}}',
                    totals: {
                        subtotal: '{{invoice.subtotal}}',
                        tax: '{{invoice.tax}}',
                        total: '{{invoice.total}}'
                    }
                }
            },
            {
                id: 'inv_hajj',
                name: 'فاتورة الحج',
                description: 'قالب مخصص لفواتير خدمات الحج والعمرة',
                category: 'invoices',
                type: 'hajj_invoice',
                icon: 'fas fa-kaaba',
                color: '#28a745',
                usage: 189,
                lastModified: new Date().toISOString(),
                isDefault: true
            },
            {
                id: 'inv_flight',
                name: 'فاتورة الطيران',
                description: 'قالب لفواتير تذاكر الطيران والحجوزات',
                category: 'invoices',
                type: 'flight_invoice',
                icon: 'fas fa-plane',
                color: '#17a2b8',
                usage: 156,
                lastModified: new Date().toISOString(),
                isDefault: true
            }
        ],
        reports: [
            {
                id: 'rep_sales',
                name: 'تقرير المبيعات الشهري',
                description: 'تقرير شامل للمبيعات والإيرادات الشهرية',
                category: 'reports',
                type: 'sales_report',
                icon: 'fas fa-chart-line',
                color: '#ffc107',
                usage: 98,
                lastModified: new Date().toISOString(),
                isDefault: true
            },
            {
                id: 'rep_customers',
                name: 'تقرير العملاء',
                description: 'تحليل شامل لبيانات العملاء والنشاط',
                category: 'reports',
                type: 'customers_report',
                icon: 'fas fa-users',
                color: '#6f42c1',
                usage: 76,
                lastModified: new Date().toISOString(),
                isDefault: true
            }
        ],
        documents: [
            {
                id: 'doc_booking_confirmation',
                name: 'تأكيد الحجز',
                description: 'وثيقة تأكيد الحجز للعملاء',
                category: 'documents',
                type: 'booking_confirmation',
                icon: 'fas fa-check-circle',
                color: '#20c997',
                usage: 134,
                lastModified: new Date().toISOString(),
                isDefault: true
            },
            {
                id: 'doc_receipt',
                name: 'إيصال استلام',
                description: 'إيصال استلام المدفوعات والوثائق',
                category: 'documents',
                type: 'receipt',
                icon: 'fas fa-receipt',
                color: '#fd7e14',
                usage: 89,
                lastModified: new Date().toISOString(),
                isDefault: true
            }
        ],
        contracts: [
            {
                id: 'cont_hajj',
                name: 'عقد الحج',
                description: 'عقد خدمات الحج والعمرة',
                category: 'contracts',
                type: 'hajj_contract',
                icon: 'fas fa-scroll',
                color: '#e83e8c',
                usage: 67,
                lastModified: new Date().toISOString(),
                isDefault: true
            },
            {
                id: 'cont_travel',
                name: 'عقد السفر',
                description: 'عقد خدمات السفر والسياحة',
                category: 'contracts',
                type: 'travel_contract',
                icon: 'fas fa-suitcase',
                color: '#6610f2',
                usage: 45,
                lastModified: new Date().toISOString(),
                isDefault: true
            }
        ]
    },

    // تهيئة النظام
    init: function() {
        console.log('🎨 تهيئة نظام القوالب...');
        this.loadTemplates();
        this.setupEventListeners();
        this.renderTemplates();
        this.startAutoSave();
        console.log('✅ تم تهيئة نظام القوالب بنجاح');
    },

    // تحميل القوالب من قاعدة البيانات
    loadTemplates: function() {
        try {
            // تحميل القوالب المحفوظة
            const savedTemplates = window.Database.getData('templates') || [];
            
            // إذا لم توجد قوالب محفوظة، استخدم القوالب الافتراضية
            if (savedTemplates.length === 0) {
                this.initializeDefaultTemplates();
            }
            
            this.templates = savedTemplates;
            console.log(`📋 تم تحميل ${this.templates.length} قالب`);
        } catch (error) {
            console.error('❌ خطأ في تحميل القوالب:', error);
            this.templates = [];
        }
    },

    // تهيئة القوالب الافتراضية
    initializeDefaultTemplates: function() {
        console.log('🔧 تهيئة القوالب الافتراضية...');
        
        const allTemplates = [];
        Object.values(this.defaultTemplates).forEach(categoryTemplates => {
            allTemplates.push(...categoryTemplates);
        });
        
        // حفظ القوالب في قاعدة البيانات
        allTemplates.forEach(template => {
            window.Database.insert('templates', template);
        });
        
        console.log(`✅ تم تهيئة ${allTemplates.length} قالب افتراضي`);
    },

    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // تبويبات التصنيف
        document.querySelectorAll('#templatesNav .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchCategory(link.dataset.category);
            });
        });

        // البحث
        const searchInput = document.getElementById('templateSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTemplates(e.target.value);
            });
        }

        // فلتر الفئات
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }
    },

    // عرض القوالب
    renderTemplates: function(templates = null) {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;

        const templatesToRender = templates || this.getAllTemplates();
        
        if (templatesToRender.length === 0) {
            grid.innerHTML = this.getEmptyStateHTML();
            return;
        }

        grid.innerHTML = templatesToRender.map(template => this.getTemplateCardHTML(template)).join('');
    },

    // الحصول على جميع القوالب
    getAllTemplates: function() {
        return window.Database.getData('templates') || [];
    },

    // تبديل الفئة
    switchCategory: function(category) {
        // تحديث التبويبات
        document.querySelectorAll('#templatesNav .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // فلترة القوالب
        if (category === 'all') {
            this.renderTemplates();
        } else {
            const filtered = this.getAllTemplates().filter(t => t.category === category);
            this.renderTemplates(filtered);
        }
    },

    // البحث في القوالب
    searchTemplates: function(query) {
        if (!query.trim()) {
            this.renderTemplates();
            return;
        }

        const filtered = this.getAllTemplates().filter(template => 
            template.name.toLowerCase().includes(query.toLowerCase()) ||
            template.description.toLowerCase().includes(query.toLowerCase())
        );
        
        this.renderTemplates(filtered);
    },

    // فلترة حسب الفئة
    filterByCategory: function(category) {
        if (!category) {
            this.renderTemplates();
            return;
        }

        const filtered = this.getAllTemplates().filter(t => t.category === category);
        this.renderTemplates(filtered);
    },

    // HTML لبطاقة القالب
    getTemplateCardHTML: function(template) {
        return `
            <div class="template-card" data-template-id="${template.id}">
                <div class="template-icon" style="background: ${template.color}">
                    <i class="${template.icon}"></i>
                </div>
                <h5>${template.name}</h5>
                <p>${template.description}</p>
                
                <div class="template-actions">
                    <button class="btn btn-primary btn-template" onclick="TemplatesSystem.useTemplate('${template.id}')">
                        <i class="fas fa-play me-1"></i>استخدام
                    </button>
                    <button class="btn btn-outline-primary btn-template" onclick="TemplatesSystem.editTemplate('${template.id}')">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </button>
                    <button class="btn btn-success btn-template" onclick="TemplatesSystem.previewTemplate('${template.id}')">
                        <i class="fas fa-eye me-1"></i>معاينة
                    </button>
                </div>
                
                <div class="template-stats">
                    <div class="stat-item">
                        <div class="stat-number">${template.usage || 0}</div>
                        <div class="stat-label">مرة استخدام</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${template.isDefault ? 'افتراضي' : 'مخصص'}</div>
                        <div class="stat-label">النوع</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${this.getRelativeTime(template.lastModified)}</div>
                        <div class="stat-label">آخر تعديل</div>
                    </div>
                </div>
            </div>
        `;
    },

    // HTML للحالة الفارغة
    getEmptyStateHTML: function() {
        return `
            <div class="col-12 text-center py-5">
                <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد قوالب</h4>
                <p class="text-muted">لم يتم العثور على قوالب مطابقة للبحث</p>
                <button class="btn btn-primary" onclick="TemplatesSystem.createNewTemplate()">
                    <i class="fas fa-plus me-2"></i>إنشاء قالب جديد
                </button>
            </div>
        `;
    },

    // حساب الوقت النسبي
    getRelativeTime: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
        
        if (diffInHours < 1) return 'الآن';
        if (diffInHours < 24) return `${diffInHours}س`;
        if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}ي`;
        return `${Math.floor(diffInHours / 168)}أ`;
    },

    // بدء الحفظ التلقائي
    startAutoSave: function() {
        if (this.config.autoSave) {
            setInterval(() => {
                this.autoSave();
            }, this.config.backupInterval);
        }
    },

    // الحفظ التلقائي
    autoSave: function() {
        try {
            const templates = this.getAllTemplates();
            localStorage.setItem('templates_backup', JSON.stringify({
                timestamp: new Date().toISOString(),
                templates: templates
            }));
            console.log('💾 تم الحفظ التلقائي للقوالب');
        } catch (error) {
            console.error('❌ خطأ في الحفظ التلقائي:', error);
        }
    }
};

    // استخدام القالب
    useTemplate: function(templateId) {
        const template = this.getAllTemplates().find(t => t.id === templateId);
        if (!template) {
            alert('❌ لم يتم العثور على القالب');
            return;
        }

        // تحديث عداد الاستخدام
        template.usage = (template.usage || 0) + 1;
        template.lastUsed = new Date().toISOString();

        // حفظ التحديث
        window.Database.update('templates', template.id, template);

        // فتح محرر القالب
        this.openTemplateEditor(template);

        console.log('🎯 تم استخدام القالب:', templateId);
    },

    // تعديل القالب
    editTemplate: function(templateId) {
        const template = this.getAllTemplates().find(t => t.id === templateId);
        if (!template) {
            alert('❌ لم يتم العثور على القالب');
            return;
        }

        // فتح محرر التعديل
        this.openTemplateEditor(template, true);

        console.log('✏️ تم فتح محرر التعديل للقالب:', templateId);
    },

    // معاينة القالب
    previewTemplate: function(templateId) {
        const template = this.getAllTemplates().find(t => t.id === templateId);
        if (!template) {
            alert('❌ لم يتم العثور على القالب');
            return;
        }

        // فتح نافذة المعاينة
        this.openTemplatePreview(template);

        console.log('👁️ تم فتح معاينة القالب:', templateId);
    },

    // فتح محرر القالب
    openTemplateEditor: function(template, editMode = false) {
        const editorUrl = `template_editor.html?template=${template.id}&mode=${editMode ? 'edit' : 'use'}`;
        const editorWindow = window.open(editorUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');

        if (!editorWindow) {
            alert('⚠️ يرجى السماح بفتح النوافذ المنبثقة لاستخدام المحرر');
        }
    },

    // فتح معاينة القالب
    openTemplatePreview: function(template) {
        // فتح نافذة المعاينة المتقدمة
        const previewUrl = `template_preview.html?template=${template.id}`;
        const previewWindow = window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (!previewWindow) {
            alert('⚠️ يرجى السماح بفتح النوافذ المنبثقة لعرض المعاينة');
            return;
        }

        console.log('👁️ تم فتح معاينة القالب المتقدمة:', template.name);
    },

    // توليد محتوى المعاينة
    generatePreviewContent: function(template) {
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>معاينة القالب - ${template.name}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <style>
                    body { font-family: 'Cairo', sans-serif; padding: 2rem; }
                    .preview-header { background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; }
                    .template-content { background: white; padding: 2rem; border: 1px solid #e9ecef; border-radius: 8px; }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h3>${template.name}</h3>
                    <p class="text-muted">${template.description}</p>
                </div>
                <div class="template-content">
                    ${this.renderTemplateContent(template)}
                </div>
            </body>
            </html>
        `;
    },

    // عرض محتوى القالب
    renderTemplateContent: function(template) {
        // محتوى تجريبي للمعاينة
        switch (template.type) {
            case 'invoice':
                return this.generateInvoicePreview();
            case 'hajj_invoice':
                return this.generateHajjInvoicePreview();
            case 'flight_invoice':
                return this.generateFlightInvoicePreview();
            case 'sales_report':
                return this.generateSalesReportPreview();
            case 'customers_report':
                return this.generateCustomersReportPreview();
            default:
                return '<p class="text-center text-muted">معاينة القالب غير متاحة حالياً</p>';
        }
    },

    // معاينة فاتورة عادية
    generateInvoicePreview: function() {
        return `
            <div class="invoice-header text-center mb-4">
                <h2>قيمة الوعد للسفريات</h2>
                <p>الرياض، المملكة العربية السعودية</p>
                <hr>
            </div>
            <div class="row mb-4">
                <div class="col-6">
                    <h5>فاتورة رقم: INV-2024-001</h5>
                    <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
                <div class="col-6 text-end">
                    <h5>بيانات العميل</h5>
                    <p>أحمد محمد السعيد<br>الرياض، السعودية<br>+966501234567</p>
                </div>
            </div>
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الخدمة</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>تذكرة طيران - الرياض إلى القاهرة</td>
                        <td>1</td>
                        <td>1,500 ريال</td>
                        <td>1,500 ريال</td>
                    </tr>
                    <tr>
                        <td>حجز فندق - 3 ليالي</td>
                        <td>1</td>
                        <td>900 ريال</td>
                        <td>900 ريال</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3">المجموع الفرعي</th>
                        <th>2,400 ريال</th>
                    </tr>
                    <tr>
                        <th colspan="3">ضريبة القيمة المضافة (15%)</th>
                        <th>360 ريال</th>
                    </tr>
                    <tr class="table-primary">
                        <th colspan="3">الإجمالي النهائي</th>
                        <th>2,760 ريال</th>
                    </tr>
                </tfoot>
            </table>
        `;
    },

    // معاينة فاتورة الحج
    generateHajjInvoicePreview: function() {
        return `
            <div class="invoice-header text-center mb-4">
                <h2>🕋 قيمة الوعد للحج والعمرة</h2>
                <p>خدمات الحج والعمرة المتميزة</p>
                <hr>
            </div>
            <div class="row mb-4">
                <div class="col-6">
                    <h5>فاتورة حج رقم: HAJ-2024-001</h5>
                    <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>موسم الحج: 1445هـ</p>
                </div>
                <div class="col-6 text-end">
                    <h5>بيانات الحاج</h5>
                    <p>عبدالله أحمد المحمد<br>جدة، السعودية<br>+966505555555</p>
                </div>
            </div>
            <table class="table table-bordered">
                <thead class="table-success">
                    <tr>
                        <th>الخدمة</th>
                        <th>المدة</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>باقة الحج الذهبية</td>
                        <td>15 يوم</td>
                        <td>12,000 ريال</td>
                        <td>12,000 ريال</td>
                    </tr>
                    <tr>
                        <td>إقامة في مكة (5 نجوم)</td>
                        <td>7 ليالي</td>
                        <td>3,500 ريال</td>
                        <td>3,500 ريال</td>
                    </tr>
                    <tr>
                        <td>إقامة في المدينة (4 نجوم)</td>
                        <td>4 ليالي</td>
                        <td>2,000 ريال</td>
                        <td>2,000 ريال</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="table-success">
                        <th colspan="3">الإجمالي النهائي</th>
                        <th>17,500 ريال</th>
                    </tr>
                </tfoot>
            </table>
            <div class="alert alert-info mt-3">
                <strong>ملاحظة:</strong> تشمل الباقة جميع الخدمات المطلوبة لأداء مناسك الحج
            </div>
        `;
    },

    // معاينة فاتورة الطيران
    generateFlightInvoicePreview: function() {
        return `
            <div class="invoice-header text-center mb-4">
                <h2>✈️ قيمة الوعد للطيران</h2>
                <p>حجوزات الطيران المحلية والدولية</p>
                <hr>
            </div>
            <div class="row mb-4">
                <div class="col-6">
                    <h5>فاتورة طيران رقم: FLT-2024-001</h5>
                    <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>رقم الحجز: ABC123</p>
                </div>
                <div class="col-6 text-end">
                    <h5>بيانات المسافر</h5>
                    <p>سارة محمد العلي<br>الدمام، السعودية<br>+966501111111</p>
                </div>
            </div>
            <div class="flight-details mb-4">
                <h5>تفاصيل الرحلة</h5>
                <div class="row">
                    <div class="col-6">
                        <strong>الذهاب:</strong> الرياض → دبي<br>
                        <strong>التاريخ:</strong> 15/03/2024<br>
                        <strong>الوقت:</strong> 08:30 - 10:45
                    </div>
                    <div class="col-6">
                        <strong>العودة:</strong> دبي → الرياض<br>
                        <strong>التاريخ:</strong> 20/03/2024<br>
                        <strong>الوقت:</strong> 14:20 - 16:35
                    </div>
                </div>
            </div>
            <table class="table table-bordered">
                <thead class="table-info">
                    <tr>
                        <th>التفاصيل</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>تذكرة ذهاب وعودة - درجة اقتصادية</td>
                        <td>1</td>
                        <td>1,200 ريال</td>
                        <td>1,200 ريال</td>
                    </tr>
                    <tr>
                        <td>رسوم المطار</td>
                        <td>1</td>
                        <td>150 ريال</td>
                        <td>150 ريال</td>
                    </tr>
                    <tr>
                        <td>تأمين السفر</td>
                        <td>1</td>
                        <td>80 ريال</td>
                        <td>80 ريال</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th colspan="3">الإجمالي النهائي</th>
                        <th>1,430 ريال</th>
                    </tr>
                </tfoot>
            </table>
        `;
    },

    // معاينة تقرير المبيعات
    generateSalesReportPreview: function() {
        return `
            <div class="report-header text-center mb-4">
                <h2>📊 تقرير المبيعات الشهري</h2>
                <p>قيمة الوعد للسفريات</p>
                <p>الفترة: مارس 2024</p>
                <hr>
            </div>
            <div class="row mb-4">
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي المبيعات</h5>
                            <h3 class="text-primary">245,000 ريال</h3>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">عدد الفواتير</h5>
                            <h3 class="text-success">156</h3>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">متوسط الفاتورة</h5>
                            <h3 class="text-info">1,571 ريال</h3>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">العملاء الجدد</h5>
                            <h3 class="text-warning">23</h3>
                        </div>
                    </div>
                </div>
            </div>
            <table class="table table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>نوع الخدمة</th>
                        <th>عدد المبيعات</th>
                        <th>إجمالي المبلغ</th>
                        <th>النسبة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>حج وعمرة</td>
                        <td>45</td>
                        <td>125,000 ريال</td>
                        <td>51%</td>
                    </tr>
                    <tr>
                        <td>تذاكر طيران</td>
                        <td>78</td>
                        <td>85,000 ريال</td>
                        <td>35%</td>
                    </tr>
                    <tr>
                        <td>حجوزات فنادق</td>
                        <td>33</td>
                        <td>35,000 ريال</td>
                        <td>14%</td>
                    </tr>
                </tbody>
            </table>
        `;
    },

    // معاينة تقرير العملاء
    generateCustomersReportPreview: function() {
        return `
            <div class="report-header text-center mb-4">
                <h2>👥 تقرير العملاء</h2>
                <p>قيمة الوعد للسفريات</p>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                <hr>
            </div>
            <div class="row mb-4">
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي العملاء</h5>
                            <h3 class="text-primary">1,247</h3>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">عملاء نشطون</h5>
                            <h3 class="text-success">892</h3>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">عملاء جدد هذا الشهر</h5>
                            <h3 class="text-info">67</h3>
                        </div>
                    </div>
                </div>
            </div>
            <table class="table table-hover">
                <thead class="table-secondary">
                    <tr>
                        <th>اسم العميل</th>
                        <th>رقم الهاتف</th>
                        <th>آخر حجز</th>
                        <th>إجمالي المشتريات</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>أحمد محمد السعيد</td>
                        <td>+966501234567</td>
                        <td>15/03/2024</td>
                        <td>12,500 ريال</td>
                        <td><span class="badge bg-success">نشط</span></td>
                    </tr>
                    <tr>
                        <td>فاطمة علي الأحمد</td>
                        <td>+966502345678</td>
                        <td>10/03/2024</td>
                        <td>8,750 ريال</td>
                        <td><span class="badge bg-success">نشط</span></td>
                    </tr>
                    <tr>
                        <td>محمد عبدالله الخالد</td>
                        <td>+966503456789</td>
                        <td>05/02/2024</td>
                        <td>15,200 ريال</td>
                        <td><span class="badge bg-warning">غير نشط</span></td>
                    </tr>
                </tbody>
            </table>
        `;
    },

    // استيراد بيانات القالب
    importTemplateData: function(templateData) {
        try {
            // التحقق من صحة البيانات
            if (!templateData.name || !templateData.type) {
                throw new Error('بيانات القالب غير مكتملة');
            }

            // إنشاء معرف جديد
            templateData.id = this.generateId();
            templateData.createdAt = new Date().toISOString();
            templateData.updatedAt = new Date().toISOString();
            templateData.isCustom = true;

            // حفظ القالب
            window.Database.insert('templates', templateData);

            // تحديث العرض
            this.renderTemplates();

            alert('✅ تم استيراد القالب بنجاح!');
            console.log('📥 تم استيراد القالب:', templateData.name);

        } catch (error) {
            console.error('❌ خطأ في استيراد القالب:', error);
            alert('❌ فشل في استيراد القالب: ' + error.message);
        }
    },

    // تصدير القالب
    exportTemplate: function(templateId) {
        const template = this.getAllTemplates().find(t => t.id === templateId);
        if (!template) {
            alert('❌ لم يتم العثور على القالب');
            return;
        }

        // إنشاء ملف JSON للتصدير
        const exportData = {
            ...template,
            exportedAt: new Date().toISOString(),
            exportedBy: 'قيمة الوعد للسفريات'
        };

        // تحويل إلى JSON
        const jsonData = JSON.stringify(exportData, null, 2);

        // إنشاء رابط التحميل
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `template_${template.name}_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('📤 تم تصدير القالب:', template.name);
    },

    // حذف القالب
    deleteTemplate: function(templateId) {
        if (!confirm('هل أنت متأكد من حذف هذا القالب؟')) {
            return;
        }

        try {
            window.Database.delete('templates', templateId);
            this.renderTemplates();
            alert('✅ تم حذف القالب بنجاح');
            console.log('🗑️ تم حذف القالب:', templateId);
        } catch (error) {
            console.error('❌ خطأ في حذف القالب:', error);
            alert('❌ فشل في حذف القالب');
        }
    }
};
};

// وظائف الإجراءات العامة
function useTemplate(templateId) {
    window.TemplatesSystem.useTemplate(templateId);
}

function editTemplate(templateId) {
    window.TemplatesSystem.editTemplate(templateId);
}

function previewTemplate(templateId) {
    window.TemplatesSystem.previewTemplate(templateId);
}

function createNewTemplate() {
    console.log('➕ إنشاء قالب جديد');
    // فتح معالج إنشاء القالب
    const editorUrl = 'template_editor.html?mode=create';
    const editorWindow = window.open(editorUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');

    if (!editorWindow) {
        alert('⚠️ يرجى السماح بفتح النوافذ المنبثقة لاستخدام المحرر');
    }
}

function importTemplate() {
    console.log('📥 استيراد قالب');
    // إنشاء حقل اختيار الملف
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.html,.xml';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const templateData = JSON.parse(e.target.result);
                    // معالجة استيراد القالب
                    window.TemplatesSystem.importTemplateData(templateData);
                } catch (error) {
                    alert('❌ خطأ في قراءة ملف القالب');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function showTemplateSettings() {
    console.log('⚙️ إعدادات القوالب');
    // فتح نافذة الإعدادات
    const settingsUrl = 'template_settings.html';
    const settingsWindow = window.open(settingsUrl, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');

    if (!settingsWindow) {
        alert('⚠️ يرجى السماح بفتح النوافذ المنبثقة لعرض الإعدادات');
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من تحميل قاعدة البيانات أولاً
    if (window.Database) {
        window.Database.init();
        window.TemplatesSystem.init();
    } else {
        console.error('❌ قاعدة البيانات غير متاحة');
    }
});
