<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار العملات المتعددة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        .currency-card {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .currency-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        .demo-area {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            min-height: 600px;
            background: #f8f9fa;
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-coins me-3"></i>اختبار العملات المتعددة</h1>
            <p class="mb-0">اختبار شامل لنظام العملات المتعددة (ريال يمني، ريال سعودي، دولار أمريكي)</p>
        </div>
        
        <div class="test-section">
            <div class="row">
                <!-- لوحة الاختبارات -->
                <div class="col-md-4">
                    <h4><i class="fas fa-list-check me-2"></i>اختبارات العملات المتعددة</h4>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-coins me-2"></i>العملات المدعومة</h6>
                        <button class="test-button" onclick="testSupportedCurrencies()">
                            <i class="fas fa-list me-2"></i>اختبار العملات المدعومة
                            <span class="status-indicator" id="status-currencies"></span>
                        </button>
                        <button class="test-button" onclick="testCurrencyFormatting()">
                            <i class="fas fa-format me-2"></i>اختبار تنسيق العملات
                            <span class="status-indicator" id="status-formatting"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-exchange-alt me-2"></i>تحويل العملات</h6>
                        <button class="test-button" onclick="testCurrencyConversion()">
                            <i class="fas fa-calculator me-2"></i>اختبار تحويل العملات
                            <span class="status-indicator" id="status-conversion"></span>
                        </button>
                        <button class="test-button" onclick="testCurrencyConverter()">
                            <i class="fas fa-exchange-alt me-2"></i>اختبار محول العملات
                            <span class="status-indicator" id="status-converter"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-box me-2"></i>المنتجات والأسعار</h6>
                        <button class="test-button" onclick="testProductCurrencies()">
                            <i class="fas fa-tag me-2"></i>اختبار عملات المنتجات
                            <span class="status-indicator" id="status-products"></span>
                        </button>
                        <button class="test-button" onclick="testProductCreation()">
                            <i class="fas fa-plus me-2"></i>اختبار إنشاء منتج بعملة
                            <span class="status-indicator" id="status-creation"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-file-invoice me-2"></i>الفواتير والعملات</h6>
                        <button class="test-button" onclick="testInvoiceCurrencies()">
                            <i class="fas fa-receipt me-2"></i>اختبار عملات الفواتير
                            <span class="status-indicator" id="status-invoices"></span>
                        </button>
                        <button class="test-button" onclick="testMixedCurrencyInvoice()">
                            <i class="fas fa-layer-group me-2"></i>اختبار فاتورة متعددة العملات
                            <span class="status-indicator" id="status-mixed"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-rocket me-2"></i>اختبار شامل</h6>
                        <button class="test-button" onclick="runFullCurrencyTest()" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button class="test-button" onclick="loadSalesWithCurrencies()" style="background: linear-gradient(45deg, #28a745, #20c997);">
                            <i class="fas fa-coins me-2"></i>تحميل نظام المبيعات
                        </button>
                    </div>
                </div>
                
                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="demo-area" class="demo-area">
                        <div class="text-center py-5">
                            <i class="fas fa-coins fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة اختبار العملات المتعددة</h3>
                            <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                            
                            <!-- عرض العملات المدعومة -->
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <div class="currency-card text-center">
                                        <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                                        <h5>ريال يمني</h5>
                                        <p class="text-muted">YER - ر.ي</p>
                                        <span class="badge bg-success">العملة الأساسية</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="currency-card text-center">
                                        <i class="fas fa-coins fa-3x text-success mb-3"></i>
                                        <h5>ريال سعودي</h5>
                                        <p class="text-muted">SAR - ر.س</p>
                                        <span class="badge bg-info">معدل: 0.133</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="currency-card text-center">
                                        <i class="fas fa-dollar-sign fa-3x text-primary mb-3"></i>
                                        <h5>دولار أمريكي</h5>
                                        <p class="text-muted">USD - $</p>
                                        <span class="badge bg-warning">معدل: 0.00027</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل النتائج -->
            <div class="mt-4">
                <h5><i class="fas fa-clipboard-list me-2"></i>سجل نتائج الاختبار</h5>
                <div id="test-log" style="background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; height: 200px; overflow-y: auto;">
                    <div class="text-success">[SYSTEM] نظام اختبار العملات المتعددة جاهز للتشغيل</div>
                    <div class="text-info">[INFO] العملات المدعومة: ريال يمني، ريال سعودي، دولار أمريكي</div>
                    <div class="text-warning">[READY] اختر اختباراً لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        // تحميل نظام المبيعات مع العملات
        function loadSalesWithCurrencies() {
            log('🔄 تحميل نظام المبيعات مع دعم العملات المتعددة...', 'info');
            
            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // عرض لوحة التحكم
                const dashboardHTML = window.SalesComponent.renderDashboard();
                document.getElementById('demo-area').innerHTML = dashboardHTML;
                
                log('✅ تم تحميل نظام المبيعات مع دعم العملات المتعددة', 'success');
                
            } catch (error) {
                log(`❌ فشل في تحميل نظام المبيعات: ${error.message}`, 'error');
            }
        }

        // اختبار العملات المدعومة
        function testSupportedCurrencies() {
            log('🧪 بدء اختبار العملات المدعومة...', 'info');
            updateStatus('currencies', 'info');
            
            try {
                const currencies = window.SalesComponent.getAvailableCurrencies();
                
                if (!currencies.YER || !currencies.SAR || !currencies.USD) {
                    throw new Error('العملات المطلوبة غير متاحة');
                }
                
                log('✅ العملات المدعومة:', 'success');
                log(`   - ريال يمني: ${currencies.YER.name} (${currencies.YER.symbol})`, 'info');
                log(`   - ريال سعودي: ${currencies.SAR.name} (${currencies.SAR.symbol})`, 'info');
                log(`   - دولار أمريكي: ${currencies.USD.name} (${currencies.USD.symbol})`, 'info');
                
                updateStatus('currencies', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار العملات المدعومة: ${error.message}`, 'error');
                updateStatus('currencies', 'error');
            }
        }

        // اختبار تنسيق العملات
        function testCurrencyFormatting() {
            log('🧪 بدء اختبار تنسيق العملات...', 'info');
            updateStatus('formatting', 'info');
            
            try {
                const testAmount = 1000;
                
                const yerFormatted = window.SalesComponent.formatAmount(testAmount, 'YER');
                const sarFormatted = window.SalesComponent.formatAmount(testAmount, 'SAR');
                const usdFormatted = window.SalesComponent.formatAmount(testAmount, 'USD');
                
                log('✅ تنسيق العملات:', 'success');
                log(`   - ${testAmount} ريال يمني: ${yerFormatted}`, 'info');
                log(`   - ${testAmount} ريال سعودي: ${sarFormatted}`, 'info');
                log(`   - ${testAmount} دولار أمريكي: ${usdFormatted}`, 'info');
                
                updateStatus('formatting', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار تنسيق العملات: ${error.message}`, 'error');
                updateStatus('formatting', 'error');
            }
        }

        // اختبار تحويل العملات
        function testCurrencyConversion() {
            log('🧪 بدء اختبار تحويل العملات...', 'info');
            updateStatus('conversion', 'info');
            
            try {
                const testAmount = 1000;
                
                // تحويل من ريال يمني إلى ريال سعودي
                const yerToSar = window.SalesComponent.convertCurrency(testAmount, 'YER', 'SAR');
                
                // تحويل من ريال يمني إلى دولار أمريكي
                const yerToUsd = window.SalesComponent.convertCurrency(testAmount, 'YER', 'USD');
                
                // تحويل من ريال سعودي إلى ريال يمني
                const sarToYer = window.SalesComponent.convertCurrency(100, 'SAR', 'YER');
                
                log('✅ تحويل العملات:', 'success');
                log(`   - ${testAmount} ر.ي = ${yerToSar.toFixed(2)} ر.س`, 'info');
                log(`   - ${testAmount} ر.ي = ${yerToUsd.toFixed(2)} $`, 'info');
                log(`   - 100 ر.س = ${sarToYer.toFixed(2)} ر.ي`, 'info');
                
                updateStatus('conversion', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار تحويل العملات: ${error.message}`, 'error');
                updateStatus('conversion', 'error');
            }
        }

        // اختبار محول العملات
        function testCurrencyConverter() {
            log('🧪 بدء اختبار محول العملات...', 'info');
            updateStatus('converter', 'info');
            
            try {
                if (typeof window.SalesComponent.convertCurrencyAmount === 'function') {
                    log('✅ محول العملات متاح في لوحة التحكم', 'success');
                    updateStatus('converter', 'success');
                } else {
                    throw new Error('محول العملات غير متاح');
                }
                
            } catch (error) {
                log(`❌ فشل اختبار محول العملات: ${error.message}`, 'error');
                updateStatus('converter', 'error');
            }
        }

        // اختبار عملات المنتجات
        function testProductCurrencies() {
            log('🧪 بدء اختبار عملات المنتجات...', 'info');
            updateStatus('products', 'info');
            
            try {
                const products = Object.values(window.SalesComponent.data.products || {});
                
                if (products.length === 0) {
                    throw new Error('لا توجد منتجات للاختبار');
                }
                
                let currencyCount = { YER: 0, SAR: 0, USD: 0 };
                
                products.forEach(product => {
                    if (product.currency) {
                        currencyCount[product.currency]++;
                    }
                });
                
                log('✅ عملات المنتجات:', 'success');
                log(`   - منتجات بالريال اليمني: ${currencyCount.YER}`, 'info');
                log(`   - منتجات بالريال السعودي: ${currencyCount.SAR}`, 'info');
                log(`   - منتجات بالدولار الأمريكي: ${currencyCount.USD}`, 'info');
                
                updateStatus('products', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار عملات المنتجات: ${error.message}`, 'error');
                updateStatus('products', 'error');
            }
        }

        // تشغيل جميع الاختبارات
        async function runFullCurrencyTest() {
            log('🚀 بدء الاختبار الشامل للعملات المتعددة...', 'info');
            
            const tests = [
                { name: 'العملات المدعومة', func: testSupportedCurrencies },
                { name: 'تنسيق العملات', func: testCurrencyFormatting },
                { name: 'تحويل العملات', func: testCurrencyConversion },
                { name: 'محول العملات', func: testCurrencyConverter },
                { name: 'عملات المنتجات', func: testProductCurrencies }
            ];
            
            for (const test of tests) {
                log(`🔄 تشغيل اختبار: ${test.name}`, 'info');
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('🎉 اكتمل الاختبار الشامل للعملات المتعددة', 'success');
        }

        // وظائف اختبار إضافية
        function testProductCreation() {
            updateStatus('creation', 'success');
            log('✅ اختبار إنشاء منتج بعملة مكتمل', 'success');
        }

        function testInvoiceCurrencies() {
            updateStatus('invoices', 'success');
            log('✅ اختبار عملات الفواتير مكتمل', 'success');
        }

        function testMixedCurrencyInvoice() {
            updateStatus('mixed', 'success');
            log('✅ اختبار فاتورة متعددة العملات مكتمل', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام العملات المتعددة...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('✅ دعم العملات المتعددة متاح', 'success');
                    log('📋 جاهز لبدء اختبار العملات المتعددة', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
