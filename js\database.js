// قاعدة البيانات المحلية لنظام إدارة وكالة السفر
// Local Database for Travel Agency Management System

console.log('🔄 تحميل قاعدة البيانات...');

// التحقق من دعم localStorage
if (typeof Storage === "undefined") {
    console.error('❌ المتصفح لا يدعم localStorage');
    alert('عذراً، متصفحك لا يدعم التخزين المحلي. يرجى استخدام متصفح حديث.');
}

// إنشاء قاعدة البيانات المحلية
window.TravelDB = {
    // البيانات المحفوظة محلياً
    data: {},
    
    // تهيئة قاعدة البيانات
    init: function() {
        try {
            // تحميل البيانات من localStorage
            const savedData = localStorage.getItem('travelAgencyDB');
            if (savedData) {
                this.data = JSON.parse(savedData);
            } else {
                this.data = {};
            }
            
            // إنشاء الجداول الأساسية إذا لم تكن موجودة
            this.createTablesIfNotExists();

            // فحص سلامة البيانات
            this.validateData();

            console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            return false;
        }
    },
    
    // إنشاء الجداول الأساسية
    createTablesIfNotExists: function() {
        const tables = [
            'customers', 'agents', 'suppliers', 'chartOfAccounts',
            'journalEntries', 'journalEntryDetails', 'bookings',
            'visaInventory', 'users', 'settings', 'products',
            'templates', 'templateCategories', 'templateUsage'
        ];
        
        tables.forEach(table => {
            if (!this.data[table]) {
                this.data[table] = [];
            }
        });
        
        this.save();
    },
    
    // حفظ البيانات في localStorage
    save: function() {
        try {
            // التحقق من حجم البيانات
            const dataString = JSON.stringify(this.data);
            const dataSize = new Blob([dataString]).size;

            // تحذير إذا كانت البيانات كبيرة (أكثر من 5MB)
            if (dataSize > 5 * 1024 * 1024) {
                console.warn('⚠️ حجم البيانات كبير:', (dataSize / 1024 / 1024).toFixed(2), 'MB');
            }

            localStorage.setItem('travelAgencyDB', dataString);
            console.log('✅ تم حفظ البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);

            // محاولة تنظيف البيانات القديمة إذا كان الخطأ بسبب امتلاء التخزين
            if (error.name === 'QuotaExceededError') {
                console.warn('⚠️ التخزين ممتلئ، محاولة تنظيف البيانات القديمة...');
                this.cleanupOldData();

                // محاولة الحفظ مرة أخرى
                try {
                    localStorage.setItem('travelAgencyDB', JSON.stringify(this.data));
                    console.log('✅ تم حفظ البيانات بعد التنظيف');
                    return true;
                } catch (retryError) {
                    console.error('❌ فشل في حفظ البيانات حتى بعد التنظيف:', retryError);
                    return false;
                }
            }

            return false;
        }
    },
    
    // الحصول على البيانات من جدول
    getData: function(table) {
        if (!this.data[table]) {
            this.data[table] = [];
        }
        return this.data[table];
    },
    
    // إدراج سجل جديد
    insert: function(table, record) {
        try {
            if (!this.data[table]) {
                this.data[table] = [];
            }
            
            // إضافة معرف تلقائي
            record.id = this.getNextId(table);
            record.created_at = new Date().toISOString();
            record.updated_at = new Date().toISOString();
            
            this.data[table].push(record);
            this.save();
            
            return record;
        } catch (error) {
            console.error('خطأ في إدراج البيانات:', error);
            return null;
        }
    },
    
    // تحديث سجل موجود
    update: function(table, id, updates) {
        try {
            const records = this.getData(table);
            const index = records.findIndex(r => r.id == id);
            
            if (index !== -1) {
                records[index] = { 
                    ...records[index], 
                    ...updates, 
                    updated_at: new Date().toISOString() 
                };
                this.save();
                return records[index];
            }
            
            return null;
        } catch (error) {
            console.error('خطأ في تحديث البيانات:', error);
            return null;
        }
    },
    
    // حذف سجل
    delete: function(table, id) {
        try {
            const records = this.getData(table);
            const index = records.findIndex(r => r.id == id);
            
            if (index !== -1) {
                const deleted = records.splice(index, 1)[0];
                this.save();
                return deleted;
            }
            
            return null;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return null;
        }
    },
    
    // البحث عن سجل بالمعرف
    findById: function(table, id) {
        const records = this.getData(table);
        return records.find(r => r.id == id);
    },
    
    // البحث في الجدول
    find: function(table, criteria) {
        const records = this.getData(table);
        return records.filter(record => {
            return Object.keys(criteria).every(key => {
                return record[key] === criteria[key];
            });
        });
    },
    
    // الحصول على المعرف التالي
    getNextId: function(table) {
        const records = this.getData(table);
        if (records.length === 0) return 1;
        
        const maxId = Math.max(...records.map(r => r.id || 0));
        return maxId + 1;
    },
    
    // مسح جدول
    clearTable: function(table) {
        this.data[table] = [];
        this.save();
    },
    
    // مسح جميع البيانات
    clearAll: function() {
        if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
            this.data = {};
            this.createTablesIfNotExists();
            console.log('تم مسح جميع البيانات');
            return true;
        }
        return false;
    },
    
    // تصدير البيانات
    export: function() {
        return JSON.stringify(this.data, null, 2);
    },
    
    // استيراد البيانات
    import: function(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            this.data = importedData;
            this.save();
            console.log('تم استيراد البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    },
    
    // إحصائيات قاعدة البيانات
    getStats: function() {
        const stats = {};
        Object.keys(this.data).forEach(table => {
            stats[table] = this.data[table].length;
        });
        return stats;
    },

    // تنظيف البيانات القديمة
    cleanupOldData: function() {
        try {
            console.log('🧹 بدء تنظيف البيانات القديمة...');

            // حذف السجلات القديمة (أكثر من سنة)
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

            let cleanedCount = 0;

            // تنظيف الجداول التي تحتوي على تواريخ
            const tablesToClean = ['journalEntries', 'journalEntryDetails', 'bookings'];

            tablesToClean.forEach(tableName => {
                if (this.data[tableName]) {
                    const originalLength = this.data[tableName].length;
                    this.data[tableName] = this.data[tableName].filter(record => {
                        const recordDate = new Date(record.created_at || record.date || '');
                        return recordDate > oneYearAgo;
                    });
                    const removedCount = originalLength - this.data[tableName].length;
                    cleanedCount += removedCount;

                    if (removedCount > 0) {
                        console.log(`🗑️ تم حذف ${removedCount} سجل قديم من جدول ${tableName}`);
                    }
                }
            });

            // تنظيف النسخ الاحتياطية القديمة من localStorage
            const backups = JSON.parse(localStorage.getItem('system_backups') || '[]');
            const recentBackups = backups.filter(backup => {
                const backupDate = new Date(backup.created_at);
                return backupDate > oneYearAgo;
            });

            if (recentBackups.length < backups.length) {
                localStorage.setItem('system_backups', JSON.stringify(recentBackups));
                console.log(`🗑️ تم حذف ${backups.length - recentBackups.length} نسخة احتياطية قديمة`);
            }

            console.log(`✅ تم تنظيف ${cleanedCount} سجل قديم`);
            return cleanedCount;

        } catch (error) {
            console.error('❌ خطأ في تنظيف البيانات:', error);
            return 0;
        }
    },

    // فحص سلامة البيانات
    validateData: function() {
        try {
            console.log('🔍 فحص سلامة البيانات...');

            const issues = [];

            // فحص الجداول الأساسية
            const requiredTables = [
                'customers', 'agents', 'suppliers', 'chartOfAccounts',
                'journalEntries', 'journalEntryDetails', 'bookings',
                'visaInventory', 'users', 'settings', 'products'
            ];

            requiredTables.forEach(table => {
                if (!this.data[table]) {
                    this.data[table] = [];
                    issues.push(`تم إنشاء جدول مفقود: ${table}`);
                }
            });

            // فحص تكامل البيانات
            if (this.data.customers) {
                this.data.customers.forEach((customer, index) => {
                    if (!customer.id) {
                        customer.id = this.getNextId('customers');
                        issues.push(`تم إضافة معرف مفقود للعميل في الفهرس ${index}`);
                    }
                    if (!customer.created_at) {
                        customer.created_at = new Date().toISOString();
                        issues.push(`تم إضافة تاريخ إنشاء للعميل ${customer.id}`);
                    }
                });
            }

            if (issues.length > 0) {
                console.log('🔧 تم إصلاح المشاكل التالية:', issues);
                this.save();
            } else {
                console.log('✅ البيانات سليمة');
            }

            return issues;

        } catch (error) {
            console.error('❌ خطأ في فحص سلامة البيانات:', error);
            return [];
        }
    }
};

// تهيئة قاعدة البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.TravelDB.init();
});

// جعل قاعدة البيانات متاحة عالمياً
window.db = window.TravelDB;

console.log('✅ تم تحميل قاعدة البيانات المحلية');
