# مرجع API - نظام قيمة الوعد

## 🔗 نظرة عامة

هذا المرجع يوثق جميع الواجهات البرمجية (APIs) المتاحة في نظام قيمة الوعد. يمكن للمطورين استخدام هذه الواجهات لتطوير ميزات جديدة أو التكامل مع أنظمة خارجية.

## 🏗️ الأنظمة الأساسية (Core APIs)

### Database API
واجهة قاعدة البيانات المحلية المتقدمة.

#### `Database.insert(table, record)`
إدراج سجل جديد في الجدول المحدد.

**المعاملات:**
- `table` (string): اسم الجدول
- `record` (object): البيانات المراد إدراجها

**القيمة المرجعة:**
- `boolean`: true إذا نجحت العملية

**مثال:**
```javascript
const customer = {
    id: 'cust_001',
    name: 'أحمد محمد',
    phone: '0501234567',
    email: '<EMAIL>'
};

const success = Database.insert('customers', customer);
if (success) {
    console.log('تم إدراج العميل بنجاح');
}
```

#### `Database.find(table, id)`
البحث عن سجل بالمعرف.

**المعاملات:**
- `table` (string): اسم الجدول
- `id` (string): معرف السجل

**القيمة المرجعة:**
- `object|null`: السجل المطلوب أو null

**مثال:**
```javascript
const customer = Database.find('customers', 'cust_001');
if (customer) {
    console.log('العميل:', customer.name);
}
```

#### `Database.findAll(table, conditions)`
البحث عن جميع السجلات مع شروط اختيارية.

**المعاملات:**
- `table` (string): اسم الجدول
- `conditions` (object, optional): شروط البحث

**القيمة المرجعة:**
- `array`: مصفوفة السجلات

**مثال:**
```javascript
// جميع العملاء
const allCustomers = Database.findAll('customers');

// عملاء من مدينة محددة
const riyadhCustomers = Database.findAll('customers', { city: 'الرياض' });
```

#### `Database.update(table, id, data)`
تحديث سجل موجود.

**المعاملات:**
- `table` (string): اسم الجدول
- `id` (string): معرف السجل
- `data` (object): البيانات الجديدة

**القيمة المرجعة:**
- `boolean`: true إذا نجحت العملية

**مثال:**
```javascript
const updated = Database.update('customers', 'cust_001', {
    phone: '0509876543',
    email: '<EMAIL>'
});
```

#### `Database.delete(table, id)`
حذف سجل.

**المعاملات:**
- `table` (string): اسم الجدول
- `id` (string): معرف السجل

**القيمة المرجعة:**
- `boolean`: true إذا نجحت العملية

### Auth API
نظام المصادقة والأمان.

#### `Auth.login(username, password)`
تسجيل الدخول.

**المعاملات:**
- `username` (string): اسم المستخدم
- `password` (string): كلمة المرور

**القيمة المرجعة:**
- `object`: معلومات المستخدم أو null

**مثال:**
```javascript
const user = Auth.login('admin', 'admin123');
if (user) {
    console.log('مرحباً', user.name);
}
```

#### `Auth.logout()`
تسجيل الخروج.

**مثال:**
```javascript
Auth.logout();
console.log('تم تسجيل الخروج');
```

#### `Auth.isAuthenticated()`
التحقق من حالة تسجيل الدخول.

**القيمة المرجعة:**
- `boolean`: true إذا كان المستخدم مسجل الدخول

#### `Auth.hashPassword(password)`
تشفير كلمة المرور.

**المعاملات:**
- `password` (string): كلمة المرور

**القيمة المرجعة:**
- `string`: كلمة المرور المشفرة

### State API
إدارة الحالة المركزية.

#### `State.get(path)`
الحصول على قيمة من الحالة.

**المعاملات:**
- `path` (string): مسار القيمة (مثل: 'user.name')

**القيمة المرجعة:**
- `any`: القيمة المطلوبة

**مثال:**
```javascript
const userName = State.get('user.name');
const isLoading = State.get('app.isLoading');
```

#### `State.set(path, value)`
تعيين قيمة في الحالة.

**المعاملات:**
- `path` (string): مسار القيمة
- `value` (any): القيمة الجديدة

**مثال:**
```javascript
State.set('user.name', 'أحمد محمد');
State.set('app.isLoading', true);
```

#### `State.subscribe(path, callback)`
الاشتراك في تغييرات قيمة معينة.

**المعاملات:**
- `path` (string): مسار القيمة
- `callback` (function): دالة التنفيذ عند التغيير

**مثال:**
```javascript
State.subscribe('user.name', (newName) => {
    console.log('اسم المستخدم الجديد:', newName);
});
```

## 🔧 الأدوات المساعدة (Utility APIs)

### Validation API
التحقق من صحة البيانات.

#### `Validation.validateEmail(email)`
التحقق من صحة البريد الإلكتروني.

**مثال:**
```javascript
const isValid = Validation.validateEmail('<EMAIL>');
```

#### `Validation.validatePhone(phone)`
التحقق من صحة رقم الهاتف.

#### `Validation.validateRequired(value)`
التحقق من وجود قيمة.

#### `Validation.validateLength(value, min, max)`
التحقق من طول النص.

### Search API
نظام البحث المتقدم.

#### `Search.search(query, options)`
البحث في البيانات.

**المعاملات:**
- `query` (string): نص البحث
- `options` (object): خيارات البحث

**مثال:**
```javascript
const results = Search.search('أحمد', {
    tables: ['customers', 'agents'],
    fuzzy: true,
    limit: 10
});
```

#### `Search.buildIndex(tableName, fields)`
بناء فهرس البحث.

#### `Search.clearHistory()`
مسح تاريخ البحث.

### Export API
نظام التصدير والطباعة.

#### `Export.exportData(tableName, format, options)`
تصدير البيانات.

**المعاملات:**
- `tableName` (string): اسم الجدول
- `format` (string): تنسيق التصدير ('excel', 'csv', 'pdf', 'json')
- `options` (object): خيارات التصدير

**مثال:**
```javascript
Export.exportData('customers', 'excel', {
    filters: { city: 'الرياض' },
    filename: 'عملاء_الرياض'
});
```

#### `Export.printData(tableName, options)`
طباعة البيانات.

### Reports API
نظام التقارير.

#### `Reports.generateReport(type, options)`
إنشاء تقرير.

**المعاملات:**
- `type` (string): نوع التقرير
- `options` (object): خيارات التقرير

**مثال:**
```javascript
Reports.generateReport('sales', {
    dateRange: { start: '2024-01-01', end: '2024-12-31' },
    groupBy: 'month'
});
```

### Cache API
نظام التخزين المؤقت.

#### `Cache.set(key, value, ttl)`
حفظ قيمة في الكاش.

**المعاملات:**
- `key` (string): مفتاح التخزين
- `value` (any): القيمة
- `ttl` (number): مدة البقاء بالمللي ثانية

#### `Cache.get(key)`
استرجاع قيمة من الكاش.

#### `Cache.clear()`
مسح جميع البيانات المؤقتة.

## 🎨 واجهة المستخدم (UI APIs)

### UI API
أدوات واجهة المستخدم.

#### `UI.showLoading(message)`
عرض شاشة التحميل.

**مثال:**
```javascript
UI.showLoading('جاري تحميل البيانات...');
```

#### `UI.hideLoading()`
إخفاء شاشة التحميل.

#### `UI.showMessage(message, type)`
عرض رسالة للمستخدم.

**المعاملات:**
- `message` (string): نص الرسالة
- `type` (string): نوع الرسالة ('success', 'error', 'warning', 'info')

#### `UI.confirm(message, callback)`
عرض رسالة تأكيد.

### Modals API
إدارة النوافذ المنبثقة.

#### `Modals.show(modalId, data)`
عرض نافذة منبثقة.

#### `Modals.hide(modalId)`
إخفاء نافذة منبثقة.

#### `Modals.create(config)`
إنشاء نافذة منبثقة جديدة.

### Navigation API
نظام التنقل.

#### `Navigation.navigateTo(page, params)`
الانتقال إلى صفحة.

**مثال:**
```javascript
Navigation.navigateTo('customers', { filter: 'active' });
```

#### `Navigation.goBack()`
العودة للصفحة السابقة.

#### `Navigation.getCurrentPage()`
الحصول على الصفحة الحالية.

## 🔒 الأمان (Security APIs)

### Security API
أدوات الأمان والحماية.

#### `Security.sanitizeInput(input)`
تنظيف المدخلات من الكود الضار.

**مثال:**
```javascript
const cleanInput = Security.sanitizeInput(userInput);
```

#### `Security.generateSecureToken(length)`
إنشاء توكن آمن.

#### `Security.validateCSRFToken(token)`
التحقق من توكن CSRF.

#### `Security.logSecurityEvent(event)`
تسجيل حدث أمني.

## 📊 الأداء (Performance APIs)

### Performance API
مراقبة وتحسين الأداء.

#### `Performance.measure(name, fn)`
قياس وقت تنفيذ دالة.

**مثال:**
```javascript
const result = Performance.measure('database_query', () => {
    return Database.findAll('customers');
});
```

#### `Performance.getReport()`
الحصول على تقرير الأداء.

#### `Performance.optimize()`
تحسين الأداء التلقائي.

## 🧪 الاختبار (Testing APIs)

### Testing API
نظام الاختبار وضمان الجودة.

#### `Testing.runAllTests()`
تشغيل جميع الاختبارات.

#### `Testing.addTest(suite, name, description, testFn)`
إضافة اختبار جديد.

**مثال:**
```javascript
Testing.addTest('utils', 'my_test', 'اختبار دالتي', () => {
    const result = myFunction('input');
    Testing.assert(result === 'expected', 'النتيجة غير متوقعة');
    return true;
});
```

## 📱 المكونات (Component APIs)

### Customers API
مكون إدارة العملاء.

#### `Customers.render(params)`
عرض مكون العملاء.

#### `Customers.addCustomer(data)`
إضافة عميل جديد.

#### `Customers.updateCustomer(id, data)`
تحديث بيانات عميل.

### Bookings API
مكون إدارة الحجوزات.

#### `Bookings.createBooking(data)`
إنشاء حجز جديد.

#### `Bookings.updateBookingStatus(id, status)`
تحديث حالة الحجز.

## 🔄 الأحداث (Events API)

### نظام الأحداث
النظام يدعم نظام أحداث شامل للتفاعل بين المكونات.

#### الأحداث المتاحة:
- `data:updated` - عند تحديث البيانات
- `user:login` - عند تسجيل الدخول
- `user:logout` - عند تسجيل الخروج
- `search:performed` - عند تنفيذ بحث
- `export:completed` - عند اكتمال التصدير
- `report:generated` - عند إنشاء تقرير

#### الاستماع للأحداث:
```javascript
document.addEventListener('data:updated', (event) => {
    console.log('تم تحديث البيانات:', event.detail);
});
```

#### إرسال أحداث مخصصة:
```javascript
const event = new CustomEvent('custom:event', {
    detail: { message: 'رسالة مخصصة' }
});
document.dispatchEvent(event);
```

## 📝 أمثلة متقدمة

### إنشاء مكون مخصص
```javascript
window.MyCustomComponent = {
    data: {
        items: [],
        loading: false
    },
    
    render: function(params = {}) {
        return `
            <div class="my-component">
                <h2>مكوني المخصص</h2>
                ${this.renderContent()}
            </div>
        `;
    },
    
    init: function() {
        this.loadData();
        this.setupEvents();
    },
    
    loadData: function() {
        this.data.loading = true;
        this.data.items = Database.findAll('my_table');
        this.data.loading = false;
    },
    
    setupEvents: function() {
        document.addEventListener('data:updated', () => {
            this.loadData();
        });
    }
};
```

### تكامل مع API خارجي
```javascript
async function integrateWithExternalAPI() {
    try {
        UI.showLoading('جاري الاتصال بالخدمة الخارجية...');
        
        const response = await fetch('https://api.example.com/data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getAPIToken()
            },
            body: JSON.stringify({
                data: Database.findAll('customers')
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            UI.showMessage('تم التزامن بنجاح', 'success');
        } else {
            throw new Error(result.message);
        }
        
    } catch (error) {
        UI.showMessage('فشل في التزامن: ' + error.message, 'error');
        Security.logSecurityEvent({
            type: 'api_error',
            message: error.message,
            timestamp: Date.now()
        });
    } finally {
        UI.hideLoading();
    }
}
```

## 🚀 نصائح للتطوير

### أفضل الممارسات
1. **استخدم التخزين المؤقت** لتحسين الأداء
2. **تحقق من صحة البيانات** قبل المعالجة
3. **استخدم نظام الأحداث** للتفاعل بين المكونات
4. **اختبر الكود** باستمرار
5. **سجل الأخطاء** للتشخيص

### تحسين الأداء
```javascript
// استخدام التخزين المؤقت
const cachedData = Cache.get('customers_list');
if (!cachedData) {
    const data = Database.findAll('customers');
    Cache.set('customers_list', data, 5 * 60 * 1000); // 5 دقائق
    return data;
}
return cachedData;

// قياس الأداء
Performance.measure('data_processing', () => {
    return processLargeDataset(data);
});
```

---

**هذا المرجع يوفر دليلاً شاملاً لجميع واجهات النظام البرمجية**

© 2024 قيمة الوعد للحلول التقنية
