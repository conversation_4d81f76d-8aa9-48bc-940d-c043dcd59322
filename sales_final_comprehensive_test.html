<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار الشامل النهائي لنظام المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            padding: 30px;
        }
        .test-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
        }
        .test-category {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 8px 0;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .test-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .test-button:hover::before {
            left: 100%;
        }
        .test-button.dashboard { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .test-button.invoices { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
        .test-button.customers { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
        .test-button.products { background: linear-gradient(45deg, #ffc107, #e0a800); color: black; }
        .test-button.reports { background: linear-gradient(45deg, #6f42c1, #5a32a3); color: white; }
        .test-button.payments { background: linear-gradient(45deg, #fd7e14, #e55a00); color: white; }
        .test-button.backup { background: linear-gradient(45deg, #6c757d, #495057); color: white; }
        .test-button.all { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }

        .test-status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            border: 2px solid white;
        }
        .test-status.success { background: #28a745; animation: pulse 2s infinite; }
        .test-status.error { background: #dc3545; animation: shake 0.5s ease-in-out; }
        .test-status.warning { background: #ffc107; }
        .test-status.testing { background: #17a2b8; animation: spin 1s linear infinite; }

        .display-area {
            background: white;
            border-radius: 15px;
            border: 3px solid #dee2e6;
            min-height: 700px;
            overflow: auto;
            position: relative;
        }

        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            border: 2px solid #333;
        }

        .results-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .metric-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .progress-ring circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }

        .progress-ring .background {
            stroke: #e9ecef;
        }

        .progress-ring .progress {
            stroke: #28a745;
            stroke-dasharray: 0 251.2;
            transition: stroke-dasharray 0.5s ease;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .feature-badge {
            display: inline-block;
            padding: 4px 8px;
            background: #007bff;
            color: white;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 5px;
        }

        .feature-badge.new { background: #28a745; }
        .feature-badge.enhanced { background: #17a2b8; }
        .feature-badge.fixed { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-rocket me-3"></i>الاختبار الشامل النهائي لنظام المبيعات</h1>
            <p class="mb-0">اختبار متقدم لجميع الوظائف والمميزات الجديدة والمحسنة</p>
            <div class="mt-3">
                <span class="feature-badge new">جديد</span>
                <span class="feature-badge enhanced">محسن</span>
                <span class="feature-badge fixed">مُصلح</span>
            </div>
        </div>

        <div class="test-grid">
            <!-- لوحة الاختبارات -->
            <div class="test-panel">
                <h4><i class="fas fa-list-check me-2"></i>قائمة الاختبارات الشاملة</h4>

                <!-- اختبارات الصفحات الأساسية -->
                <div class="test-category">
                    <h6><i class="fas fa-window-restore me-2"></i>الصفحات الأساسية</h6>

                    <button class="test-button dashboard" onclick="testFeature('dashboard')">
                        <span class="test-status" id="status-dashboard"></span>
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        <span class="feature-badge enhanced">محسن</span>
                    </button>

                    <button class="test-button invoices" onclick="testFeature('invoices')">
                        <span class="test-status" id="status-invoices"></span>
                        <i class="fas fa-file-invoice me-2"></i>الفواتير
                        <span class="feature-badge fixed">مُصلح</span>
                    </button>

                    <button class="test-button customers" onclick="testFeature('customers')">
                        <span class="test-status" id="status-customers"></span>
                        <i class="fas fa-users me-2"></i>العملاء
                        <span class="feature-badge new">جديد</span>
                    </button>

                    <button class="test-button products" onclick="testFeature('products')">
                        <span class="test-status" id="status-products"></span>
                        <i class="fas fa-box me-2"></i>المنتجات
                        <span class="feature-badge new">جديد</span>
                    </button>

                    <button class="test-button reports" onclick="testFeature('reports')">
                        <span class="test-status" id="status-reports"></span>
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                        <span class="feature-badge enhanced">محسن</span>
                    </button>

                    <button class="test-button payments" onclick="testFeature('payments')">
                        <span class="test-status" id="status-payments"></span>
                        <i class="fas fa-credit-card me-2"></i>المدفوعات
                        <span class="feature-badge new">جديد</span>
                    </button>
                </div>

                <!-- اختبارات الوظائف المتقدمة -->
                <div class="test-category">
                    <h6><i class="fas fa-cogs me-2"></i>الوظائف المتقدمة</h6>

                    <button class="test-button invoices" onclick="testFeature('create-invoice')">
                        <span class="test-status" id="status-create-invoice"></span>
                        <i class="fas fa-plus me-2"></i>إنشاء فاتورة
                        <span class="feature-badge fixed">مُصلح</span>
                    </button>

                    <button class="test-button customers" onclick="testFeature('customer-management')">
                        <span class="test-status" id="status-customer-management"></span>
                        <i class="fas fa-user-edit me-2"></i>إدارة العملاء
                        <span class="feature-badge new">جديد</span>
                    </button>

                    <button class="test-button products" onclick="testFeature('product-management')">
                        <span class="test-status" id="status-product-management"></span>
                        <i class="fas fa-box-open me-2"></i>إدارة المنتجات
                        <span class="feature-badge new">جديد</span>
                    </button>

                    <button class="test-button payments" onclick="testFeature('payment-processing')">
                        <span class="test-status" id="status-payment-processing"></span>
                        <i class="fas fa-money-bill-wave me-2"></i>معالجة المدفوعات
                        <span class="feature-badge new">جديد</span>
                    </button>

                    <button class="test-button backup" onclick="testFeature('backup-system')">
                        <span class="test-status" id="status-backup-system"></span>
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطية
                        <span class="feature-badge new">جديد</span>
                    </button>
                </div>

                <!-- اختبارات شاملة -->
                <div class="test-category">
                    <h6><i class="fas fa-rocket me-2"></i>اختبارات شاملة</h6>

                    <button class="test-button all" onclick="runFullTest()">
                        <span class="test-status" id="status-all"></span>
                        <i class="fas fa-play me-2"></i>اختبار شامل كامل
                    </button>

                    <button class="test-button reports" onclick="testFeature('performance')">
                        <span class="test-status" id="status-performance"></span>
                        <i class="fas fa-tachometer-alt me-2"></i>اختبار الأداء
                    </button>

                    <button class="test-button dashboard" onclick="testFeature('error-handling')">
                        <span class="test-status" id="status-error-handling"></span>
                        <i class="fas fa-shield-alt me-2"></i>معالجة الأخطاء
                    </button>
                </div>

                <hr>

                <button class="btn btn-outline-secondary w-100 mt-3" onclick="clearAllResults()">
                    <i class="fas fa-trash me-2"></i>مسح جميع النتائج
                </button>

                <button class="btn btn-outline-info w-100 mt-2" onclick="exportTestResults()">
                    <i class="fas fa-download me-2"></i>تصدير نتائج الاختبار
                </button>
            </div>

            <!-- منطقة العرض والنتائج -->
            <div>
                <div class="display-area" id="main-content">
                    <div class="text-center p-5">
                        <i class="fas fa-rocket fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">منطقة عرض الاختبارات</h3>
                        <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                        <div class="progress-ring mt-4">
                            <svg width="120" height="120">
                                <circle class="background" cx="60" cy="60" r="40"></circle>
                                <circle class="progress" cx="60" cy="60" r="40" id="progress-circle"></circle>
                            </svg>
                            <div class="text-center mt-2">
                                <span id="progress-percentage">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- لوحة النتائج -->
                <div class="results-panel">
                    <h5><i class="fas fa-chart-pie me-2"></i>نتائج الاختبار المباشرة</h5>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h6>الاختبارات المكتملة</h6>
                                <div class="h4 text-primary" id="completed-tests">0</div>
                                <small class="text-muted">من أصل <span id="total-tests">15</span></small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h6>معدل النجاح</h6>
                                <div class="h4 text-success" id="success-rate">0%</div>
                                <small class="text-muted">نسبة الاختبارات الناجحة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h6>متوسط الأداء</h6>
                                <div class="h4 text-info" id="avg-performance">0ms</div>
                                <small class="text-muted">متوسط وقت الاستجابة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h6>الأخطاء المكتشفة</h6>
                                <div class="h4 text-danger" id="error-count">0</div>
                                <small class="text-muted">عدد الأخطاء الإجمالي</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجل الأحداث -->
                <div class="log-area" id="test-log">
                    <div class="text-success">[SYSTEM] نظام الاختبار الشامل النهائي جاهز للتشغيل</div>
                    <div class="text-info">[INFO] تم تحميل جميع المكونات والتحسينات الجديدة</div>
                    <div class="text-warning">[READY] اختر اختباراً من القائمة لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>

    <script>
        // متغيرات الاختبار الشامل
        let testResults = {
            dashboard: null,
            invoices: null,
            customers: null,
            products: null,
            reports: null,
            payments: null,
            'create-invoice': null,
            'customer-management': null,
            'product-management': null,
            'payment-processing': null,
            'backup-system': null,
            performance: null,
            'error-handling': null
        };

        let testMetrics = {
            completedTests: 0,
            successfulTests: 0,
            totalLoadTime: 0,
            errorCount: 0,
            totalTests: 15
        };

        // وظائف السجل المحسنة
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger',
                debug: 'text-secondary'
            };

            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;

            // تأثير بصري للرسائل المهمة
            if (type === 'error' || type === 'success') {
                logEntry.style.fontWeight = 'bold';
                logEntry.style.animation = 'pulse 0.5s ease-in-out';
            }
        }

        // تحديث حالة الاختبار
        function updateTestStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `test-status ${status}`;
            }
        }

        // تحديث المقاييس
        function updateMetrics() {
            document.getElementById('completed-tests').textContent = testMetrics.completedTests;

            const successRate = testMetrics.completedTests > 0 ?
                Math.round((testMetrics.successfulTests / testMetrics.completedTests) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';

            const avgPerformance = testMetrics.completedTests > 0 ?
                Math.round(testMetrics.totalLoadTime / testMetrics.completedTests) : 0;
            document.getElementById('avg-performance').textContent = avgPerformance + 'ms';

            document.getElementById('error-count').textContent = testMetrics.errorCount;

            // تحديث الدائرة التقدمية
            const progressPercentage = Math.round((testMetrics.completedTests / testMetrics.totalTests) * 100);
            updateProgressRing(progressPercentage);
        }

        // تحديث دائرة التقدم
        function updateProgressRing(percentage) {
            const circle = document.getElementById('progress-circle');
            const circumference = 2 * Math.PI * 40; // نصف القطر = 40
            const offset = circumference - (percentage / 100) * circumference;

            circle.style.strokeDasharray = `${circumference} ${circumference}`;
            circle.style.strokeDashoffset = offset;

            document.getElementById('progress-percentage').textContent = percentage + '%';

            // تغيير اللون حسب النسبة
            if (percentage >= 80) {
                circle.style.stroke = '#28a745';
            } else if (percentage >= 60) {
                circle.style.stroke = '#ffc107';
            } else {
                circle.style.stroke = '#dc3545';
            }
        }

        // اختبار ميزة واحدة
        async function testFeature(featureId) {
            log(`🧪 بدء اختبار: ${getFeatureName(featureId)}`, 'info');
            updateTestStatus(featureId, 'testing');

            const startTime = performance.now();
            let success = false;
            let errorMessage = '';

            try {
                // التحقق من وجود المكون
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }

                // تهيئة المكون
                window.SalesComponent.init();

                // تشغيل الاختبار المحدد
                success = await runSpecificTest(featureId);

                if (success) {
                    log(`✅ نجح اختبار: ${getFeatureName(featureId)}`, 'success');
                    updateTestStatus(featureId, 'success');
                    testMetrics.successfulTests++;
                } else {
                    throw new Error('فشل في تنفيذ الاختبار');
                }

            } catch (error) {
                errorMessage = error.message;
                log(`❌ فشل اختبار ${getFeatureName(featureId)}: ${errorMessage}`, 'error');
                updateTestStatus(featureId, 'error');
                testMetrics.errorCount++;
            }

            const endTime = performance.now();
            const loadTime = Math.round(endTime - startTime);

            // حفظ النتائج
            testResults[featureId] = {
                success: success,
                loadTime: loadTime,
                error: errorMessage,
                timestamp: new Date().toISOString()
            };

            // تحديث المقاييس
            testMetrics.completedTests++;
            testMetrics.totalLoadTime += loadTime;

            updateMetrics();

            log(`📊 وقت التنفيذ: ${loadTime}ms`, 'debug');
        }

        // تشغيل اختبار محدد
        async function runSpecificTest(featureId) {
            switch (featureId) {
                case 'dashboard':
                    return await testDashboard();
                case 'invoices':
                    return await testInvoices();
                case 'customers':
                    return await testCustomers();
                case 'products':
                    return await testProducts();
                case 'reports':
                    return await testReports();
                case 'payments':
                    return await testPayments();
                case 'create-invoice':
                    return await testCreateInvoice();
                case 'customer-management':
                    return await testCustomerManagement();
                case 'product-management':
                    return await testProductManagement();
                case 'payment-processing':
                    return await testPaymentProcessing();
                case 'backup-system':
                    return await testBackupSystem();
                case 'performance':
                    return await testPerformance();
                case 'error-handling':
                    return await testErrorHandling();
                default:
                    return false;
            }
        }

        // اختبارات محددة
        async function testDashboard() {
            window.SalesComponent.render({ view: 'dashboard' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            if (!content || content.innerHTML.trim().length === 0) {
                throw new Error('لم يتم إنشاء محتوى لوحة التحكم');
            }

            // التحقق من وجود البطاقات الإحصائية
            const cards = content.querySelectorAll('.card');
            if (cards.length < 4) {
                throw new Error('بطاقات الإحصائيات غير مكتملة');
            }

            return true;
        }

        async function testInvoices() {
            window.SalesComponent.render({ view: 'invoices' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            const table = content.querySelector('table');
            if (!table) {
                throw new Error('جدول الفواتير غير موجود');
            }

            return true;
        }

        async function testCustomers() {
            window.SalesComponent.render({ view: 'customers' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            const table = content.querySelector('table');
            if (!table) {
                throw new Error('جدول العملاء غير موجود');
            }

            return true;
        }

        async function testProducts() {
            window.SalesComponent.render({ view: 'products' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            const table = content.querySelector('table');
            if (!table) {
                throw new Error('جدول المنتجات غير موجود');
            }

            return true;
        }

        async function testReports() {
            window.SalesComponent.render({ view: 'reports' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            const cards = content.querySelectorAll('.card');
            if (cards.length < 3) {
                throw new Error('بطاقات التقارير غير مكتملة');
            }

            return true;
        }

        async function testPayments() {
            window.SalesComponent.render({ view: 'payments' });
            await new Promise(resolve => setTimeout(resolve, 200));

            const content = document.getElementById('main-content');
            const cards = content.querySelectorAll('.card');
            if (cards.length < 2) {
                throw new Error('صفحة المدفوعات غير مكتملة');
            }

            return true;
        }

        async function testCreateInvoice() {
            // اختبار فتح نافذة إنشاء فاتورة
            if (typeof window.SalesComponent.showCreateInvoiceModal !== 'function') {
                throw new Error('وظيفة إنشاء الفاتورة غير متاحة');
            }

            return true;
        }

        async function testCustomerManagement() {
            // اختبار وظائف إدارة العملاء
            if (typeof window.SalesComponent.showCreateCustomerModal !== 'function') {
                throw new Error('وظيفة إدارة العملاء غير متاحة');
            }

            return true;
        }

        async function testProductManagement() {
            // اختبار وظائف إدارة المنتجات
            if (typeof window.SalesComponent.showCreateProductModal !== 'function') {
                throw new Error('وظيفة إدارة المنتجات غير متاحة');
            }

            return true;
        }

        async function testPaymentProcessing() {
            // اختبار معالجة المدفوعات
            if (typeof window.SalesComponent.showAddPaymentModal !== 'function') {
                throw new Error('وظيفة معالجة المدفوعات غير متاحة');
            }

            return true;
        }

        async function testBackupSystem() {
            // اختبار نظام النسخ الاحتياطية
            if (typeof window.SalesComponent.createBackup !== 'function') {
                throw new Error('نظام النسخ الاحتياطية غير متاح');
            }

            return true;
        }

        async function testPerformance() {
            // اختبار الأداء
            const startTime = performance.now();

            for (let i = 0; i < 5; i++) {
                window.SalesComponent.render({ view: 'dashboard' });
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            if (totalTime > 2000) {
                throw new Error('الأداء بطيء جداً');
            }

            return true;
        }

        async function testErrorHandling() {
            // اختبار معالجة الأخطاء
            if (typeof window.SalesComponent.handleError !== 'function') {
                throw new Error('معالج الأخطاء غير متاح');
            }

            return true;
        }

        // الحصول على اسم الميزة
        function getFeatureName(featureId) {
            const names = {
                dashboard: 'لوحة التحكم',
                invoices: 'الفواتير',
                customers: 'العملاء',
                products: 'المنتجات',
                reports: 'التقارير',
                payments: 'المدفوعات',
                'create-invoice': 'إنشاء فاتورة',
                'customer-management': 'إدارة العملاء',
                'product-management': 'إدارة المنتجات',
                'payment-processing': 'معالجة المدفوعات',
                'backup-system': 'النسخ الاحتياطية',
                performance: 'اختبار الأداء',
                'error-handling': 'معالجة الأخطاء'
            };
            return names[featureId] || featureId;
        }

        // تشغيل اختبار شامل
        async function runFullTest() {
            log('🚀 بدء الاختبار الشامل الكامل...', 'info');
            updateTestStatus('all', 'testing');

            const features = Object.keys(testResults);

            for (const feature of features) {
                await testFeature(feature);
                await new Promise(resolve => setTimeout(resolve, 300)); // انتظار بين الاختبارات
            }

            updateTestStatus('all', 'success');
            log('🎉 اكتمل الاختبار الشامل الكامل', 'success');

            // عرض ملخص النتائج
            showFinalSummary();
        }

        // عرض ملخص النتائج النهائي
        function showFinalSummary() {
            log('📊 ملخص نتائج الاختبار الشامل:', 'info');

            Object.keys(testResults).forEach(feature => {
                const result = testResults[feature];
                if (result) {
                    const status = result.success ? '✅' : '❌';
                    log(`${status} ${getFeatureName(feature)}: ${result.loadTime}ms`,
                        result.success ? 'success' : 'error');
                }
            });

            const successRate = Math.round((testMetrics.successfulTests / testMetrics.completedTests) * 100);
            log(`📈 معدل النجاح الإجمالي: ${successRate}%`,
                successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error');

            log(`⚡ متوسط الأداء: ${Math.round(testMetrics.totalLoadTime / testMetrics.completedTests)}ms`, 'info');

            if (successRate >= 90) {
                log('🏆 ممتاز! النظام يعمل بشكل مثالي', 'success');
            } else if (successRate >= 70) {
                log('👍 جيد! النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة', 'warning');
            } else {
                log('⚠️ يحتاج النظام إلى مراجعة وإصلاحات', 'error');
            }
        }

        // مسح جميع النتائج
        function clearAllResults() {
            testResults = Object.fromEntries(Object.keys(testResults).map(key => [key, null]));
            testMetrics = {
                completedTests: 0,
                successfulTests: 0,
                totalLoadTime: 0,
                errorCount: 0,
                totalTests: 15
            };

            // إعادة تعيين حالات الاختبار
            Object.keys(testResults).forEach(feature => {
                updateTestStatus(feature, '');
            });
            updateTestStatus('all', '');

            updateMetrics();

            document.getElementById('test-log').innerHTML = `
                <div class="text-success">[SYSTEM] تم مسح جميع النتائج</div>
                <div class="text-info">[INFO] جاهز لاختبارات جديدة</div>
            `;

            document.getElementById('main-content').innerHTML = `
                <div class="text-center p-5">
                    <i class="fas fa-rocket fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">منطقة عرض الاختبارات</h3>
                    <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                    <div class="progress-ring mt-4">
                        <svg width="120" height="120">
                            <circle class="background" cx="60" cy="60" r="40"></circle>
                            <circle class="progress" cx="60" cy="60" r="40" id="progress-circle"></circle>
                        </svg>
                        <div class="text-center mt-2">
                            <span id="progress-percentage">0%</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // تصدير نتائج الاختبار
        function exportTestResults() {
            const reportData = {
                timestamp: new Date().toISOString(),
                summary: {
                    completedTests: testMetrics.completedTests,
                    successfulTests: testMetrics.successfulTests,
                    successRate: Math.round((testMetrics.successfulTests / testMetrics.completedTests) * 100),
                    avgPerformance: Math.round(testMetrics.totalLoadTime / testMetrics.completedTests),
                    errorCount: testMetrics.errorCount
                },
                detailedResults: testResults,
                systemInfo: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                }
            };

            const reportJson = JSON.stringify(reportData, null, 2);
            const blob = new Blob([reportJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `sales_test_report_${new Date().toISOString().split('T')[0]}.json`;
            a.click();

            URL.revokeObjectURL(url);

            log('📄 تم تصدير تقرير الاختبار', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام المبيعات...', 'info');

                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('🎨 تحسينات واجهة المستخدم متاحة', 'success');
                    log('🔧 نظام الإصلاح متاح', 'success');
                    log('📋 جاهز لبدء الاختبارات الشاملة', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>