# نظام القوالب المتقدم - قيمة الوعد للسفريات

## نظرة عامة

نظام القوالب المتقدم هو حل شامل ومتطور لإدارة وتصميم القوالب في نظام المحاسبة للسفريات. يوفر النظام واجهات حديثة ومرنة لإنشاء وتحرير ومعاينة وطباعة القوالب المختلفة.

## المميزات الرئيسية

### 🎨 محرر القوالب المتقدم
- **واجهة سحب وإفلات**: تصميم القوالب بسهولة عبر سحب العناصر
- **محرر مرئي**: تحرير القوالب بشكل مرئي مع معاينة فورية
- **عناصر متنوعة**: نصوص، عناوين، جداول، صور، ومتغيرات ديناميكية
- **خصائص قابلة للتخصيص**: تحكم كامل في الألوان والخطوط والأحجام

### 📄 أنواع القوالب المدعومة
- **قوالب الفواتير**: فواتير عادية، فواتير الحج، فواتير الطيران
- **قوالب التقارير**: تقارير المبيعات، تقارير العملاء، التقارير المالية
- **قوالب الوثائق**: تأكيدات الحجز، إيصالات الدفع، الشهادات
- **قوالب العقود**: عقود الحج، عقود السفر، الاتفاقيات

### 👁️ نظام المعاينة والطباعة
- **معاينة مباشرة**: عرض القالب كما سيظهر عند الطباعة
- **تحكم في الزوم**: تكبير وتصغير المعاينة (25% - 200%)
- **أحجام ورق متعددة**: A4، Letter، Legal
- **اتجاهات مختلفة**: عمودي وأفقي
- **جودة طباعة قابلة للتخصيص**: عالية، متوسطة، منخفضة

### ⚙️ إعدادات شاملة
- **إعدادات عامة**: الحفظ التلقائي، المعاينة المباشرة، الشبكة المساعدة
- **إعدادات التصميم**: الخطوط الافتراضية، الألوان، الأحجام
- **إدارة القوالب**: تصنيف وتنظيم القوالب
- **النسخ الاحتياطي**: حفظ واستعادة القوالب

## الملفات المكونة للنظام

### الواجهات الرئيسية
- `templates_system.html` - الواجهة الرئيسية لإدارة القوالب
- `template_editor.html` - محرر القوالب المتقدم
- `template_preview.html` - نظام المعاينة والطباعة
- `template_settings.html` - إعدادات النظام

### ملفات JavaScript
- `js/templates-system.js` - النظام الأساسي لإدارة القوالب
- `js/template-editor.js` - محرك محرر القوالب
- `js/template-preview.js` - نظام المعاينة والطباعة
- `js/database.js` - قاعدة البيانات المحدثة

## كيفية الاستخدام

### 1. الوصول إلى النظام
```html
<!-- فتح النظام الرئيسي -->
<a href="templates_system.html">نظام القوالب</a>
```

### 2. إنشاء قالب جديد
1. انقر على زر "إنشاء قالب جديد"
2. اختر نوع القالب (فاتورة، تقرير، وثيقة، عقد)
3. استخدم محرر السحب والإفلات لتصميم القالب
4. احفظ القالب

### 3. تحرير قالب موجود
1. انقر على "تعديل" بجانب القالب المطلوب
2. استخدم المحرر لإجراء التعديلات
3. احفظ التغييرات

### 4. معاينة وطباعة
1. انقر على "معاينة" بجانب القالب
2. أدخل البيانات المطلوبة في الشريط الجانبي
3. اختر إعدادات الطباعة
4. انقر على "طباعة" أو "تصدير PDF"

## المتغيرات المدعومة

### متغيرات الشركة
- `{{company.name}}` - اسم الشركة
- `{{company.address}}` - عنوان الشركة
- `{{company.phone}}` - هاتف الشركة
- `{{company.email}}` - بريد الشركة

### متغيرات الفاتورة
- `{{invoice.number}}` - رقم الفاتورة
- `{{invoice.date}}` - تاريخ الفاتورة
- `{{invoice.due_date}}` - تاريخ الاستحقاق
- `{{invoice.total}}` - إجمالي المبلغ

### متغيرات العميل
- `{{customer.name}}` - اسم العميل
- `{{customer.address}}` - عنوان العميل
- `{{customer.phone}}` - هاتف العميل
- `{{customer.email}}` - بريد العميل

## التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **Bootstrap 5.3.0** - إطار العمل للتصميم المتجاوب
- **Font Awesome 6.4.0** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

### JavaScript
- **Vanilla JavaScript** - البرمجة الأساسية
- **LocalStorage** - تخزين البيانات محلياً
- **Drag & Drop API** - واجهة السحب والإفلات
- **Print API** - واجهة الطباعة

### المميزات التقنية
- **تصميم متجاوب** - يعمل على جميع الأجهزة
- **دعم RTL** - دعم كامل للغة العربية
- **معمارية معيارية** - كود منظم وقابل للصيانة
- **أداء محسن** - تحميل سريع وتفاعل سلس

## إعدادات قاعدة البيانات

### جداول جديدة
```javascript
// جدول القوالب
templates: {
    id: 'string',
    name: 'string',
    description: 'string',
    type: 'string',
    category: 'string',
    content: 'string',
    variables: 'object',
    settings: 'object',
    isActive: 'boolean',
    isCustom: 'boolean',
    createdAt: 'string',
    updatedAt: 'string',
    usage: 'number',
    lastUsed: 'string'
}

// جدول فئات القوالب
templateCategories: {
    id: 'string',
    name: 'string',
    description: 'string',
    icon: 'string',
    color: 'string',
    isActive: 'boolean'
}

// جدول استخدام القوالب
templateUsage: {
    id: 'string',
    templateId: 'string',
    userId: 'string',
    usedAt: 'string',
    context: 'object'
}
```

## الاختبار والتطوير

### اختبار النظام
1. افتح `templates_system.html` في المتصفح
2. تأكد من عمل جميع الوظائف الأساسية
3. اختبر إنشاء وتحرير القوالب
4. تأكد من عمل المعاينة والطباعة

### التطوير المستقبلي
- [ ] تصدير القوالب بصيغة PDF
- [ ] استيراد القوالب من ملفات خارجية
- [ ] نظام الصلاحيات للقوالب
- [ ] قوالب متقدمة مع JavaScript
- [ ] تكامل مع أنظمة البريد الإلكتروني

## الدعم والصيانة

### متطلبات النظام
- متصفح حديث يدعم HTML5 و CSS3
- JavaScript مفعل
- دقة شاشة لا تقل عن 1024x768

### استكشاف الأخطاء
1. **القوالب لا تظهر**: تأكد من تحميل قاعدة البيانات
2. **المحرر لا يعمل**: تحقق من دعم المتصفح للسحب والإفلات
3. **الطباعة لا تعمل**: تأكد من إعدادات المتصفح

### التحديثات
- يتم حفظ إعدادات النظام في LocalStorage
- النسخ الاحتياطية تتم تلقائياً كل 30 ثانية
- يمكن استعادة البيانات من النسخ الاحتياطية

## الترخيص

هذا النظام مطور خصيصاً لشركة قيمة الوعد للسفريات ويخضع لسياسات الشركة الداخلية.

---

**تم التطوير بواسطة:** فريق التطوير - قيمة الوعد للسفريات  
**تاريخ الإصدار:** مارس 2024  
**الإصدار:** 1.0.0
