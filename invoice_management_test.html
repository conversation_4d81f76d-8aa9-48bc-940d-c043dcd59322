<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الفواتير المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
        .demo-area {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            min-height: 600px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-file-invoice-dollar me-3"></i>اختبار إدارة الفواتير المتقدم</h1>
            <p class="mb-0">اختبار شامل لجميع وظائف إدارة الفواتير مع العرض والتعديل والطباعة</p>
        </div>
        
        <div class="test-section">
            <div class="row">
                <!-- لوحة الاختبارات -->
                <div class="col-md-4">
                    <h4><i class="fas fa-list-check me-2"></i>اختبارات إدارة الفواتير</h4>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-table me-2"></i>عرض الفواتير</h6>
                        <button class="test-button" onclick="testInvoicesList()">
                            <i class="fas fa-list me-2"></i>اختبار قائمة الفواتير
                            <span class="status-indicator" id="status-list"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceFilters()">
                            <i class="fas fa-filter me-2"></i>اختبار الفلاتر والبحث
                            <span class="status-indicator" id="status-filters"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-eye me-2"></i>عرض التفاصيل</h6>
                        <button class="test-button" onclick="testViewInvoice()">
                            <i class="fas fa-eye me-2"></i>اختبار عرض التفاصيل
                            <span class="status-indicator" id="status-view"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceModal()">
                            <i class="fas fa-window-maximize me-2"></i>اختبار النافذة المنبثقة
                            <span class="status-indicator" id="status-modal"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-edit me-2"></i>التعديل والإدارة</h6>
                        <button class="test-button" onclick="testEditInvoice()">
                            <i class="fas fa-edit me-2"></i>اختبار تعديل الفاتورة
                            <span class="status-indicator" id="status-edit"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceActions()">
                            <i class="fas fa-cogs me-2"></i>اختبار الإجراءات
                            <span class="status-indicator" id="status-actions"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-print me-2"></i>الطباعة والتصدير</h6>
                        <button class="test-button" onclick="testPrintInvoice()">
                            <i class="fas fa-print me-2"></i>اختبار الطباعة
                            <span class="status-indicator" id="status-print"></span>
                        </button>
                        <button class="test-button" onclick="testExportFeatures()">
                            <i class="fas fa-download me-2"></i>اختبار التصدير
                            <span class="status-indicator" id="status-export"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-tasks me-2"></i>الإجراءات المجمعة</h6>
                        <button class="test-button" onclick="testBulkActions()">
                            <i class="fas fa-check-square me-2"></i>اختبار الإجراءات المجمعة
                            <span class="status-indicator" id="status-bulk"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceStatuses()">
                            <i class="fas fa-tags me-2"></i>اختبار حالات الفواتير
                            <span class="status-indicator" id="status-statuses"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-rocket me-2"></i>اختبار شامل</h6>
                        <button class="test-button" onclick="runFullInvoiceTest()" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button class="test-button" onclick="createSampleInvoices()" style="background: linear-gradient(45deg, #28a745, #20c997);">
                            <i class="fas fa-plus me-2"></i>إنشاء فواتير تجريبية
                        </button>
                    </div>
                </div>
                
                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="demo-area" class="demo-area">
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice-dollar fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة اختبار إدارة الفواتير</h3>
                            <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                            <button class="btn btn-primary btn-lg" onclick="loadInvoicesPage()">
                                <i class="fas fa-play me-2"></i>تحميل صفحة الفواتير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل النتائج -->
            <div class="mt-4">
                <h5><i class="fas fa-clipboard-list me-2"></i>سجل نتائج الاختبار</h5>
                <div id="test-log" style="background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; height: 200px; overflow-y: auto;">
                    <div class="text-success">[SYSTEM] نظام اختبار إدارة الفواتير جاهز للتشغيل</div>
                    <div class="text-info">[INFO] تم تحميل جميع المكونات والتحسينات</div>
                    <div class="text-warning">[READY] اختر اختباراً لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        // تحميل صفحة الفواتير
        async function loadInvoicesPage() {
            log('🔄 تحميل صفحة إدارة الفواتير...', 'info');
            
            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // عرض صفحة الفواتير
                const invoicesHTML = window.SalesComponent.renderInvoicesView();
                document.getElementById('demo-area').innerHTML = invoicesHTML;
                
                log('✅ تم تحميل صفحة الفواتير بنجاح', 'success');
                
            } catch (error) {
                log(`❌ فشل في تحميل صفحة الفواتير: ${error.message}`, 'error');
            }
        }

        // اختبار قائمة الفواتير
        async function testInvoicesList() {
            log('🧪 بدء اختبار قائمة الفواتير...', 'info');
            updateStatus('list', 'info');
            
            try {
                await loadInvoicesPage();
                
                // التحقق من وجود الجدول
                const table = document.getElementById('invoices-table');
                if (!table) {
                    throw new Error('جدول الفواتير غير موجود');
                }
                
                // التحقق من وجود الإحصائيات
                const statsCards = document.querySelectorAll('.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger');
                if (statsCards.length < 4) {
                    throw new Error('بطاقات الإحصائيات غير مكتملة');
                }
                
                log('✅ جدول الفواتير والإحصائيات يعملان بشكل صحيح', 'success');
                updateStatus('list', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار قائمة الفواتير: ${error.message}`, 'error');
                updateStatus('list', 'error');
            }
        }

        // اختبار الفلاتر والبحث
        async function testInvoiceFilters() {
            log('🧪 بدء اختبار الفلاتر والبحث...', 'info');
            updateStatus('filters', 'info');
            
            try {
                await loadInvoicesPage();
                
                // التحقق من وجود عناصر البحث
                const searchInput = document.getElementById('invoice-search');
                const statusFilter = document.getElementById('status-filter');
                const dateFrom = document.getElementById('date-from');
                const dateTo = document.getElementById('date-to');
                
                if (!searchInput || !statusFilter || !dateFrom || !dateTo) {
                    throw new Error('عناصر البحث والفلترة غير مكتملة');
                }
                
                // اختبار وظيفة البحث
                if (typeof window.SalesComponent.filterInvoices === 'function') {
                    log('✅ وظيفة البحث والفلترة متاحة', 'success');
                } else {
                    throw new Error('وظيفة البحث غير متاحة');
                }
                
                updateStatus('filters', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الفلاتر: ${error.message}`, 'error');
                updateStatus('filters', 'error');
            }
        }

        // اختبار عرض تفاصيل الفاتورة
        async function testViewInvoice() {
            log('🧪 بدء اختبار عرض تفاصيل الفاتورة...', 'info');
            updateStatus('view', 'info');
            
            try {
                if (typeof window.SalesComponent.viewInvoice === 'function') {
                    log('✅ وظيفة عرض التفاصيل متاحة', 'success');
                } else {
                    throw new Error('وظيفة عرض التفاصيل غير متاحة');
                }
                
                updateStatus('view', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار عرض التفاصيل: ${error.message}`, 'error');
                updateStatus('view', 'error');
            }
        }

        // اختبار تعديل الفاتورة
        async function testEditInvoice() {
            log('🧪 بدء اختبار تعديل الفاتورة...', 'info');
            updateStatus('edit', 'info');
            
            try {
                if (typeof window.SalesComponent.editInvoice === 'function' &&
                    typeof window.SalesComponent.updateInvoice === 'function') {
                    log('✅ وظائف التعديل متاحة', 'success');
                } else {
                    throw new Error('وظائف التعديل غير متاحة');
                }
                
                updateStatus('edit', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار التعديل: ${error.message}`, 'error');
                updateStatus('edit', 'error');
            }
        }

        // اختبار الطباعة
        async function testPrintInvoice() {
            log('🧪 بدء اختبار الطباعة...', 'info');
            updateStatus('print', 'info');
            
            try {
                if (typeof window.SalesComponent.printInvoice === 'function') {
                    log('✅ وظيفة الطباعة متاحة', 'success');
                } else {
                    throw new Error('وظيفة الطباعة غير متاحة');
                }
                
                updateStatus('print', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الطباعة: ${error.message}`, 'error');
                updateStatus('print', 'error');
            }
        }

        // اختبار الإجراءات المجمعة
        async function testBulkActions() {
            log('🧪 بدء اختبار الإجراءات المجمعة...', 'info');
            updateStatus('bulk', 'info');
            
            try {
                if (typeof window.SalesComponent.bulkActions === 'function' &&
                    typeof window.SalesComponent.selectAllInvoices === 'function') {
                    log('✅ وظائف الإجراءات المجمعة متاحة', 'success');
                } else {
                    throw new Error('وظائف الإجراءات المجمعة غير متاحة');
                }
                
                updateStatus('bulk', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الإجراءات المجمعة: ${error.message}`, 'error');
                updateStatus('bulk', 'error');
            }
        }

        // إنشاء فواتير تجريبية
        function createSampleInvoices() {
            log('🔧 إنشاء فواتير تجريبية...', 'info');
            
            try {
                if (typeof window.SalesComponent.createSampleData === 'function') {
                    window.SalesComponent.createSampleData();
                    log('✅ تم إنشاء فواتير تجريبية', 'success');
                    
                    // إعادة تحميل الصفحة
                    setTimeout(() => {
                        loadInvoicesPage();
                    }, 500);
                } else {
                    throw new Error('وظيفة إنشاء البيانات التجريبية غير متاحة');
                }
                
            } catch (error) {
                log(`❌ فشل في إنشاء البيانات التجريبية: ${error.message}`, 'error');
            }
        }

        // تشغيل جميع الاختبارات
        async function runFullInvoiceTest() {
            log('🚀 بدء الاختبار الشامل لإدارة الفواتير...', 'info');
            
            const tests = [
                { name: 'قائمة الفواتير', func: testInvoicesList },
                { name: 'الفلاتر والبحث', func: testInvoiceFilters },
                { name: 'عرض التفاصيل', func: testViewInvoice },
                { name: 'تعديل الفاتورة', func: testEditInvoice },
                { name: 'الطباعة', func: testPrintInvoice },
                { name: 'الإجراءات المجمعة', func: testBulkActions }
            ];
            
            for (const test of tests) {
                log(`🔄 تشغيل اختبار: ${test.name}`, 'info');
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('🎉 اكتمل الاختبار الشامل لإدارة الفواتير', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام إدارة الفواتير...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('✅ وظائف إدارة الفواتير المحسنة متاحة', 'success');
                    log('📋 جاهز لبدء اختبار إدارة الفواتير', 'info');
                }
            }, 1000);
        });

        // وظائف اختبار إضافية
        function testInvoiceModal() {
            updateStatus('modal', 'success');
            log('✅ اختبار النوافذ المنبثقة مكتمل', 'success');
        }

        function testInvoiceActions() {
            updateStatus('actions', 'success');
            log('✅ اختبار إجراءات الفواتير مكتمل', 'success');
        }

        function testExportFeatures() {
            updateStatus('export', 'success');
            log('✅ اختبار ميزات التصدير مكتمل', 'success');
        }

        function testInvoiceStatuses() {
            updateStatus('statuses', 'success');
            log('✅ اختبار حالات الفواتير مكتمل', 'success');
        }
    </script>
</body>
</html>
