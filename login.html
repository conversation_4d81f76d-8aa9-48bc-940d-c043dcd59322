<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - قمة الوعد للسفريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            min-height: 600px;
            display: flex;
        }

        .login-left {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 60px 40px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .login-right {
            padding: 60px 40px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .company-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.9);
            position: relative;
            z-index: 2;
        }

        .company-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .company-tagline {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .features-list {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 2;
        }

        .features-list li {
            padding: 10px 0;
            font-size: 1.1rem;
        }

        .features-list i {
            margin-left: 10px;
            color: rgba(255, 255, 255, 0.8);
        }

        .login-form {
            max-width: 400px;
            width: 100%;
        }

        .form-title {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }

        .form-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #667eea;
        }

        .form-title p {
            color: #666;
            font-size: 1.1rem;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .form-floating input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-floating label {
            padding: 20px 15px;
            font-size: 1rem;
            color: #666;
        }

        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            font-size: 0.95rem;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #764ba2;
        }

        .loading-spinner {
            display: none;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 10px;
            }
            
            .login-left {
                padding: 40px 20px;
                min-height: 300px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .company-name {
                font-size: 2rem;
            }
            
            .company-logo {
                font-size: 3rem;
            }
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .demo-credentials {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }

        .demo-credentials h6 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .demo-credentials p {
            margin: 5px 0;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- الجانب الأيسر - معلومات الشركة -->
        <div class="login-left">
            <div class="company-logo">
                <i class="fas fa-plane"></i>
            </div>
            <h1 class="company-name">قمة الوعد</h1>
            <p class="company-tagline">للسفريات والسياحة</p>
            
            <ul class="features-list">
                <li><i class="fas fa-check-circle"></i> نظام إدارة شامل</li>
                <li><i class="fas fa-users"></i> إدارة العملاء والوكلاء</li>
                <li><i class="fas fa-calculator"></i> نظام محاسبي متكامل</li>
                <li><i class="fas fa-chart-bar"></i> تقارير مفصلة</li>
                <li><i class="fas fa-shield-alt"></i> أمان وموثوقية</li>
            </ul>
        </div>

        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <div class="login-right">
            <div class="login-form">
                <div class="form-title">
                    <h2>مرحباً بك</h2>
                    <p>سجل دخولك للوصول إلى النظام</p>
                </div>

                <div id="alert-container"></div>

                <form id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                        <label for="username">اسم المستخدم</label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                        <label for="password">كلمة المرور</label>
                    </div>

                    <div class="remember-forgot">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <span class="login-text">تسجيل الدخول</span>
                        <span class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </form>

                <div class="demo-credentials">
                    <h6><i class="fas fa-info-circle"></i> بيانات تجريبية</h6>
                    <p><strong>المستخدم:</strong> admin</p>
                    <p><strong>كلمة المرور:</strong> admin123</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.querySelector('.btn-login');
            const loginText = document.querySelector('.login-text');
            const loadingSpinner = document.querySelector('.loading-spinner');
            
            // إظهار حالة التحميل
            loginBtn.disabled = true;
            loginText.style.display = 'none';
            loadingSpinner.style.display = 'inline';
            
            // محاكاة عملية تسجيل الدخول
            setTimeout(() => {
                if (username === 'admin' && password === 'admin123') {
                    // حفظ حالة تسجيل الدخول
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('currentUser', JSON.stringify({
                        username: username,
                        fullName: 'مدير النظام',
                        role: 'admin',
                        loginTime: new Date().toISOString()
                    }));
                    
                    showAlert('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
                    
                    // إعادة تعيين حالة الزر
                    loginBtn.disabled = false;
                    loginText.style.display = 'inline';
                    loadingSpinner.style.display = 'none';
                }
            }, 1500);
        });
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // التحقق من حالة تسجيل الدخول
        if (localStorage.getItem('isLoggedIn') === 'true') {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
