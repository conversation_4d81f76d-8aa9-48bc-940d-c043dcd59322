<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء الفواتير المحسن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-file-invoice me-3"></i>اختبار إنشاء الفواتير المحسن</h1>
            <p class="mb-0">اختبار شامل لجميع مميزات إنشاء الفواتير الجديدة والمحسنة</p>
        </div>
        
        <div class="test-section">
            <div class="row">
                <!-- لوحة الاختبارات -->
                <div class="col-md-4">
                    <h4><i class="fas fa-list-check me-2"></i>اختبارات الفواتير</h4>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة</h6>
                        <button class="test-button" onclick="testCreateInvoice()">
                            <i class="fas fa-file-plus me-2"></i>اختبار إنشاء فاتورة
                            <span class="status-indicator" id="status-create"></span>
                        </button>
                        <button class="test-button" onclick="testInvoiceValidation()">
                            <i class="fas fa-check-circle me-2"></i>اختبار التحقق من البيانات
                            <span class="status-indicator" id="status-validation"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-shopping-cart me-2"></i>إدارة العناصر</h6>
                        <button class="test-button" onclick="testAddItems()">
                            <i class="fas fa-plus me-2"></i>اختبار إضافة العناصر
                            <span class="status-indicator" id="status-add-items"></span>
                        </button>
                        <button class="test-button" onclick="testCalculations()">
                            <i class="fas fa-calculator me-2"></i>اختبار الحسابات
                            <span class="status-indicator" id="status-calculations"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-eye me-2"></i>المعاينة والحفظ</h6>
                        <button class="test-button" onclick="testPreview()">
                            <i class="fas fa-eye me-2"></i>اختبار المعاينة
                            <span class="status-indicator" id="status-preview"></span>
                        </button>
                        <button class="test-button" onclick="testSave()">
                            <i class="fas fa-save me-2"></i>اختبار الحفظ
                            <span class="status-indicator" id="status-save"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-rocket me-2"></i>اختبار شامل</h6>
                        <button class="test-button" onclick="runFullTest()" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                    </div>
                </div>
                
                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="main-content" style="min-height: 600px; border: 2px solid #dee2e6; border-radius: 15px; padding: 20px;">
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة اختبار الفواتير</h3>
                            <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                            <button class="btn btn-primary btn-lg" onclick="testCreateInvoice()">
                                <i class="fas fa-play me-2"></i>بدء الاختبار
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل النتائج -->
            <div class="mt-4">
                <h5><i class="fas fa-clipboard-list me-2"></i>سجل نتائج الاختبار</h5>
                <div id="test-log" style="background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; height: 200px; overflow-y: auto;">
                    <div class="text-success">[SYSTEM] نظام اختبار الفواتير جاهز للتشغيل</div>
                    <div class="text-info">[INFO] تم تحميل جميع المكونات والتحسينات</div>
                    <div class="text-warning">[READY] اختر اختباراً لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        // اختبار إنشاء فاتورة
        async function testCreateInvoice() {
            log('🧪 بدء اختبار إنشاء فاتورة جديدة...', 'info');
            updateStatus('create', 'info');
            
            try {
                // التحقق من وجود المكون
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // فتح نافذة إنشاء الفاتورة
                window.SalesComponent.showCreateInvoiceModal();
                
                // انتظار قليل للتأكد من فتح النافذة
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // التحقق من وجود النافذة
                const modal = document.getElementById('createInvoiceModal');
                if (!modal) {
                    throw new Error('فشل في فتح نافذة إنشاء الفاتورة');
                }
                
                log('✅ تم فتح نافذة إنشاء الفاتورة بنجاح', 'success');
                updateStatus('create', 'success');
                
                // التحقق من العناصر المطلوبة
                const requiredElements = [
                    'createInvoiceForm',
                    'invoiceItems',
                    'invoice-progress'
                ];
                
                for (const elementId of requiredElements) {
                    if (!document.getElementById(elementId)) {
                        throw new Error(`العنصر المطلوب غير موجود: ${elementId}`);
                    }
                }
                
                log('✅ جميع عناصر النافذة موجودة ومحملة', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار إنشاء الفاتورة: ${error.message}`, 'error');
                updateStatus('create', 'error');
            }
        }

        // اختبار التحقق من البيانات
        async function testInvoiceValidation() {
            log('🧪 بدء اختبار التحقق من البيانات...', 'info');
            updateStatus('validation', 'info');
            
            try {
                // فتح النافذة أولاً
                await testCreateInvoice();
                
                // محاولة حفظ فاتورة فارغة
                const saveButton = document.querySelector('button[onclick*="saveInvoice"]');
                if (saveButton) {
                    saveButton.click();
                    
                    // انتظار قليل لظهور رسالة التحقق
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                    log('✅ تم اختبار التحقق من البيانات الفارغة', 'success');
                }
                
                updateStatus('validation', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار التحقق: ${error.message}`, 'error');
                updateStatus('validation', 'error');
            }
        }

        // اختبار إضافة العناصر
        async function testAddItems() {
            log('🧪 بدء اختبار إضافة العناصر...', 'info');
            updateStatus('add-items', 'info');
            
            try {
                // التأكد من وجود النافذة
                let modal = document.getElementById('createInvoiceModal');
                if (!modal) {
                    await testCreateInvoice();
                    modal = document.getElementById('createInvoiceModal');
                }
                
                // اختبار إضافة عنصر
                if (typeof window.SalesComponent.addInvoiceItem === 'function') {
                    window.SalesComponent.addInvoiceItem();
                    
                    // التحقق من إضافة العنصر
                    const itemsContainer = document.getElementById('invoiceItems');
                    if (itemsContainer && itemsContainer.children.length > 0) {
                        log('✅ تم إضافة عنصر جديد بنجاح', 'success');
                    } else {
                        throw new Error('فشل في إضافة العنصر');
                    }
                } else {
                    throw new Error('وظيفة إضافة العناصر غير متاحة');
                }
                
                updateStatus('add-items', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار إضافة العناصر: ${error.message}`, 'error');
                updateStatus('add-items', 'error');
            }
        }

        // اختبار الحسابات
        async function testCalculations() {
            log('🧪 بدء اختبار الحسابات...', 'info');
            updateStatus('calculations', 'info');
            
            try {
                // التأكد من وجود النافذة
                let modal = document.getElementById('createInvoiceModal');
                if (!modal) {
                    await testCreateInvoice();
                    await testAddItems();
                }
                
                // اختبار حساب الإجماليات
                if (typeof window.SalesComponent.calculateInvoiceTotal === 'function') {
                    const result = window.SalesComponent.calculateInvoiceTotal();
                    
                    if (result && typeof result === 'object') {
                        log(`✅ تم حساب الإجماليات: ${JSON.stringify(result)}`, 'success');
                    } else {
                        log('⚠️ تم تشغيل الحسابات ولكن لا توجد بيانات', 'warning');
                    }
                } else {
                    throw new Error('وظيفة الحسابات غير متاحة');
                }
                
                updateStatus('calculations', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الحسابات: ${error.message}`, 'error');
                updateStatus('calculations', 'error');
            }
        }

        // اختبار المعاينة
        async function testPreview() {
            log('🧪 بدء اختبار المعاينة...', 'info');
            updateStatus('preview', 'info');
            
            try {
                if (typeof window.SalesComponent.previewInvoice === 'function') {
                    // محاولة فتح المعاينة
                    window.SalesComponent.previewInvoice();
                    
                    log('✅ تم اختبار وظيفة المعاينة', 'success');
                } else {
                    throw new Error('وظيفة المعاينة غير متاحة');
                }
                
                updateStatus('preview', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار المعاينة: ${error.message}`, 'error');
                updateStatus('preview', 'error');
            }
        }

        // اختبار الحفظ
        async function testSave() {
            log('🧪 بدء اختبار الحفظ...', 'info');
            updateStatus('save', 'info');
            
            try {
                if (typeof window.SalesComponent.saveInvoice === 'function' && 
                    typeof window.SalesComponent.saveDraftInvoice === 'function') {
                    
                    log('✅ وظائف الحفظ متاحة', 'success');
                } else {
                    throw new Error('وظائف الحفظ غير متاحة');
                }
                
                updateStatus('save', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الحفظ: ${error.message}`, 'error');
                updateStatus('save', 'error');
            }
        }

        // تشغيل جميع الاختبارات
        async function runFullTest() {
            log('🚀 بدء الاختبار الشامل لجميع وظائف الفواتير...', 'info');
            
            const tests = [
                { name: 'إنشاء فاتورة', func: testCreateInvoice },
                { name: 'التحقق من البيانات', func: testInvoiceValidation },
                { name: 'إضافة العناصر', func: testAddItems },
                { name: 'الحسابات', func: testCalculations },
                { name: 'المعاينة', func: testPreview },
                { name: 'الحفظ', func: testSave }
            ];
            
            for (const test of tests) {
                log(`🔄 تشغيل اختبار: ${test.name}`, 'info');
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('🎉 اكتمل الاختبار الشامل لجميع وظائف الفواتير', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام الفواتير...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('✅ وظائف الفواتير المحسنة متاحة', 'success');
                    log('📋 جاهز لبدء اختبار الفواتير', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
