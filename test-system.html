<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - قمة الوعد للسفريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .test-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .test-result {
            padding: 0.5rem;
            border-radius: 8px;
            margin: 0.25rem 0;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-vial me-3 text-primary"></i>
                        اختبار النظام
                    </h1>
                    <p class="lead text-muted">فحص شامل لجميع مكونات نظام إدارة وكالة السفر</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            اختبار قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3" onclick="testDatabase()">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبار
                        </button>
                        <div id="databaseTestResults"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            اختبار JavaScript
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-3" onclick="testJavaScript()">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبار
                        </button>
                        <div id="jsTestResults"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-desktop me-2"></i>
                            اختبار واجهة المستخدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info mb-3" onclick="testUI()">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبار
                        </button>
                        <div id="uiTestResults"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card test-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            اختبار الأداء
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning mb-3" onclick="testPerformance()">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبار
                        </button>
                        <div id="performanceTestResults"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            تشغيل جميع الاختبارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-dark btn-lg mb-3" onclick="runAllTests()">
                            <i class="fas fa-rocket me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <div id="allTestsResults"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="index.html" class="btn btn-primary btn-lg">
                    <i class="fas fa-home me-2"></i>العودة إلى النظام الرئيسي
                </a>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>

    <script>
        // وظائف الاختبار
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${getIconForType(type)} me-2"></i>${message}`;
            container.appendChild(resultDiv);
        }

        function getIconForType(type) {
            switch(type) {
                case 'success': return 'check-circle';
                case 'error': return 'times-circle';
                case 'warning': return 'exclamation-triangle';
                default: return 'info-circle';
            }
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // اختبار قاعدة البيانات
        function testDatabase() {
            clearResults('databaseTestResults');
            addTestResult('databaseTestResults', 'بدء اختبار قاعدة البيانات...', 'info');

            try {
                // اختبار وجود قاعدة البيانات
                if (window.TravelDB) {
                    addTestResult('databaseTestResults', '✅ قاعدة البيانات TravelDB موجودة', 'success');
                } else {
                    addTestResult('databaseTestResults', '❌ قاعدة البيانات TravelDB غير موجودة', 'error');
                }

                // اختبار تهيئة قاعدة البيانات
                if (window.TravelDB && typeof window.TravelDB.init === 'function') {
                    const initResult = window.TravelDB.init();
                    if (initResult) {
                        addTestResult('databaseTestResults', '✅ تهيئة قاعدة البيانات نجحت', 'success');
                    } else {
                        addTestResult('databaseTestResults', '❌ فشل في تهيئة قاعدة البيانات', 'error');
                    }
                }

                // اختبار الوظائف الأساسية
                if (window.TravelDB && typeof window.TravelDB.getData === 'function') {
                    addTestResult('databaseTestResults', '✅ وظيفة getData متاحة', 'success');
                } else {
                    addTestResult('databaseTestResults', '❌ وظيفة getData غير متاحة', 'error');
                }

                // اختبار localStorage
                if (typeof Storage !== "undefined") {
                    addTestResult('databaseTestResults', '✅ localStorage مدعوم', 'success');
                } else {
                    addTestResult('databaseTestResults', '❌ localStorage غير مدعوم', 'error');
                }

                addTestResult('databaseTestResults', 'تم الانتهاء من اختبار قاعدة البيانات', 'info');

            } catch (error) {
                addTestResult('databaseTestResults', `❌ خطأ في اختبار قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        // اختبار JavaScript
        function testJavaScript() {
            clearResults('jsTestResults');
            addTestResult('jsTestResults', 'بدء اختبار JavaScript...', 'info');

            try {
                // اختبار الوظائف الأساسية
                const functions = [
                    'showDashboard', 'showCustomers', 'showAddCustomer', 
                    'showErrorMessage', 'showSuccessMessage', 'initializeDatabase'
                ];

                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addTestResult('jsTestResults', `✅ وظيفة ${funcName} متاحة`, 'success');
                    } else {
                        addTestResult('jsTestResults', `❌ وظيفة ${funcName} غير متاحة`, 'error');
                    }
                });

                // اختبار المتغيرات العامة
                if (typeof window.db !== 'undefined') {
                    addTestResult('jsTestResults', '✅ متغير db متاح', 'success');
                } else {
                    addTestResult('jsTestResults', '⚠️ متغير db غير متاح', 'warning');
                }

                addTestResult('jsTestResults', 'تم الانتهاء من اختبار JavaScript', 'info');

            } catch (error) {
                addTestResult('jsTestResults', `❌ خطأ في اختبار JavaScript: ${error.message}`, 'error');
            }
        }

        // اختبار واجهة المستخدم
        function testUI() {
            clearResults('uiTestResults');
            addTestResult('uiTestResults', 'بدء اختبار واجهة المستخدم...', 'info');

            try {
                // اختبار العناصر الأساسية
                const elements = [
                    'main-content', 'dashboard'
                ];

                elements.forEach(elementId => {
                    const element = document.getElementById(elementId);
                    if (element) {
                        addTestResult('uiTestResults', `✅ عنصر ${elementId} موجود`, 'success');
                    } else {
                        addTestResult('uiTestResults', `❌ عنصر ${elementId} غير موجود`, 'error');
                    }
                });

                // اختبار CSS
                const computedStyle = window.getComputedStyle(document.body);
                if (computedStyle.fontFamily.includes('Cairo')) {
                    addTestResult('uiTestResults', '✅ خط Cairo محمل', 'success');
                } else {
                    addTestResult('uiTestResults', '⚠️ خط Cairo غير محمل', 'warning');
                }

                // اختبار Bootstrap
                if (typeof bootstrap !== 'undefined') {
                    addTestResult('uiTestResults', '✅ Bootstrap محمل', 'success');
                } else {
                    addTestResult('uiTestResults', '❌ Bootstrap غير محمل', 'error');
                }

                addTestResult('uiTestResults', 'تم الانتهاء من اختبار واجهة المستخدم', 'info');

            } catch (error) {
                addTestResult('uiTestResults', `❌ خطأ في اختبار واجهة المستخدم: ${error.message}`, 'error');
            }
        }

        // اختبار الأداء
        function testPerformance() {
            clearResults('performanceTestResults');
            addTestResult('performanceTestResults', 'بدء اختبار الأداء...', 'info');

            try {
                // اختبار الذاكرة
                if (performance.memory) {
                    const memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    if (memoryUsage < 50) {
                        addTestResult('performanceTestResults', `✅ استخدام الذاكرة جيد: ${memoryUsage}MB`, 'success');
                    } else if (memoryUsage < 100) {
                        addTestResult('performanceTestResults', `⚠️ استخدام الذاكرة متوسط: ${memoryUsage}MB`, 'warning');
                    } else {
                        addTestResult('performanceTestResults', `❌ استخدام الذاكرة عالي: ${memoryUsage}MB`, 'error');
                    }
                } else {
                    addTestResult('performanceTestResults', '⚠️ معلومات الذاكرة غير متاحة', 'warning');
                }

                // اختبار سرعة التحميل
                const loadTime = performance.now();
                if (loadTime < 1000) {
                    addTestResult('performanceTestResults', `✅ وقت التحميل ممتاز: ${Math.round(loadTime)}ms`, 'success');
                } else if (loadTime < 3000) {
                    addTestResult('performanceTestResults', `⚠️ وقت التحميل مقبول: ${Math.round(loadTime)}ms`, 'warning');
                } else {
                    addTestResult('performanceTestResults', `❌ وقت التحميل بطيء: ${Math.round(loadTime)}ms`, 'error');
                }

                addTestResult('performanceTestResults', 'تم الانتهاء من اختبار الأداء', 'info');

            } catch (error) {
                addTestResult('performanceTestResults', `❌ خطأ في اختبار الأداء: ${error.message}`, 'error');
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            clearResults('allTestsResults');
            addTestResult('allTestsResults', '🚀 بدء تشغيل جميع الاختبارات...', 'info');

            setTimeout(() => {
                testDatabase();
                addTestResult('allTestsResults', '✅ تم الانتهاء من اختبار قاعدة البيانات', 'success');
            }, 500);

            setTimeout(() => {
                testJavaScript();
                addTestResult('allTestsResults', '✅ تم الانتهاء من اختبار JavaScript', 'success');
            }, 1000);

            setTimeout(() => {
                testUI();
                addTestResult('allTestsResults', '✅ تم الانتهاء من اختبار واجهة المستخدم', 'success');
            }, 1500);

            setTimeout(() => {
                testPerformance();
                addTestResult('allTestsResults', '✅ تم الانتهاء من اختبار الأداء', 'success');
                addTestResult('allTestsResults', '🎉 تم الانتهاء من جميع الاختبارات!', 'success');
            }, 2000);
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addTestResult('allTestsResults', '✅ تم تحميل صفحة الاختبار بنجاح', 'success');
            }, 500);
        });
    </script>
</body>
</html>
