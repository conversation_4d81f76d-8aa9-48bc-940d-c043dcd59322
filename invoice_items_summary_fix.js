/**
 * إصلاح ربط عناصر الفاتورة بملخص الفاتورة
 * Invoice Items to Summary Linking Fix
 */

console.log('🔧 تحميل إصلاح ربط عناصر الفاتورة بالملخص...');

// إصلاح ربط عناصر الفاتورة بالملخص
function fixInvoiceItemsSummaryLinking() {
    if (typeof window.SalesComponent === 'undefined') return;

    // تحسين وظيفة حساب إجمالي العنصر
    const originalCalculateItemTotal = window.SalesComponent.calculateItemTotal;
    window.SalesComponent.calculateItemTotal = function(inputElement) {
        if (!inputElement) return;

        const item = inputElement.closest('.invoice-item');
        if (!item) return;

        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (!quantityInput || !priceInput || !totalInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;

        // التحقق من صحة القيم
        if (quantity < 0) {
            quantityInput.value = 1;
            this.showNotification('الكمية يجب أن تكون أكبر من صفر', 'warning');
            return;
        }

        if (price < 0) {
            priceInput.value = 0;
            this.showNotification('السعر يجب أن يكون أكبر من أو يساوي صفر', 'warning');
            return;
        }

        const total = quantity * price;
        totalInput.value = total.toFixed(2);

        // تأثير بصري للتحديث
        totalInput.style.transition = 'all 0.3s ease';
        totalInput.style.backgroundColor = '#28a745';
        totalInput.style.color = 'white';
        totalInput.style.transform = 'scale(1.05)';

        setTimeout(() => {
            totalInput.style.backgroundColor = '';
            totalInput.style.color = '';
            totalInput.style.transform = 'scale(1)';
        }, 600);

        // حساب إجمالي الفاتورة فوراً
        this.calculateInvoiceTotal();

        // تحديث شريط التقدم
        this.updateInvoiceProgress(90);

        console.log(`💰 تم تحديث إجمالي العنصر: ${quantity} × ${price} = ${total}`);
    };

    // تحسين وظيفة حساب إجمالي الفاتورة
    const originalCalculateInvoiceTotal = window.SalesComponent.calculateInvoiceTotal;
    window.SalesComponent.calculateInvoiceTotal = function() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) {
            console.warn('عنصر invoiceItems غير موجود');
            return { subtotal: 0, tax: 0, total: 0, itemCount: 0 };
        }

        let subtotal = 0;
        let itemCount = 0;
        const mainCurrency = this.invoiceSettings?.currentInvoiceCurrency || 'YER';

        console.log('🧮 بدء حساب إجمالي الفاتورة...');

        // حساب المجموع الفرعي
        Array.from(itemsContainer.children).forEach((item, index) => {
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            if (quantityInput && priceInput && totalInput) {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                let total = parseFloat(totalInput.value) || 0;

                // إعادة حساب الإجمالي إذا لم يكن صحيح
                const calculatedTotal = quantity * price;
                if (Math.abs(total - calculatedTotal) > 0.01) {
                    total = calculatedTotal;
                    totalInput.value = total.toFixed(2);
                }

                if (quantity > 0 && price >= 0 && total > 0) {
                    // الحصول على عملة العنصر
                    const productSelect = item.querySelector('.item-product') || item.querySelector('.product-select');
                    let itemCurrency = 'YER';
                    
                    if (productSelect && productSelect.value) {
                        const product = this.data.products[productSelect.value];
                        itemCurrency = product?.currency || 'YER';
                    } else {
                        const currencyInput = item.querySelector('.item-currency');
                        if (currencyInput && currencyInput.value) {
                            itemCurrency = currencyInput.value;
                        }
                    }

                    // تحويل إلى العملة الرئيسية
                    const convertedTotal = this.convertCurrency(total, itemCurrency, mainCurrency);
                    subtotal += convertedTotal;
                    itemCount++;

                    console.log(`📦 عنصر ${index + 1}: ${quantity} × ${price} = ${total} ${itemCurrency} (${convertedTotal.toFixed(2)} ${mainCurrency})`);
                }
            }
        });

        // حساب الخصم
        const discountElement = document.getElementById('discountAmount');
        const discount = discountElement ? parseFloat(discountElement.value) || 0 : 0;
        const validDiscount = Math.min(discount, subtotal);

        if (discountElement && discount > subtotal) {
            discountElement.value = subtotal.toFixed(2);
            this.showNotification('تم تعديل الخصم ليكون أقل من أو يساوي المجموع الفرعي', 'warning');
        }

        // حساب المجموع بعد الخصم
        const subtotalAfterDiscount = subtotal - validDiscount;

        // حساب الضريبة
        let tax = 0;
        const taxEnabled = this.invoiceSettings?.taxEnabled !== false;
        const showTax = this.invoiceSettings?.showTax !== false;
        
        if (taxEnabled && showTax) {
            const taxRate = this.invoiceSettings?.taxRate || this.data.settings?.taxRate || 0.15;
            tax = subtotalAfterDiscount * taxRate;
        }

        // حساب الإجمالي النهائي
        const total = subtotalAfterDiscount + tax;

        // تحديث عناصر العرض
        this.updateInvoiceSummaryDisplay(subtotal, tax, total, itemCount, mainCurrency);

        console.log(`📊 ملخص الفاتورة: المجموع الفرعي=${subtotal.toFixed(2)}, الخصم=${validDiscount.toFixed(2)}, الضريبة=${tax.toFixed(2)}, الإجمالي=${total.toFixed(2)} ${mainCurrency}`);

        return { subtotal, tax, total, itemCount, discount: validDiscount, currency: mainCurrency };
    };

    // إضافة معالجات أحداث للعناصر الموجودة
    function addEventListenersToExistingItems() {
        const itemsContainer = document.getElementById('invoiceItems');
        if (!itemsContainer) return;

        Array.from(itemsContainer.children).forEach(item => {
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const productSelect = item.querySelector('.item-product') || item.querySelector('.product-select');

            if (quantityInput) {
                quantityInput.removeEventListener('input', handleItemChange);
                quantityInput.addEventListener('input', handleItemChange);
                quantityInput.removeEventListener('change', handleItemChange);
                quantityInput.addEventListener('change', handleItemChange);
            }

            if (priceInput) {
                priceInput.removeEventListener('input', handleItemChange);
                priceInput.addEventListener('input', handleItemChange);
                priceInput.removeEventListener('change', handleItemChange);
                priceInput.addEventListener('change', handleItemChange);
            }

            if (productSelect) {
                productSelect.removeEventListener('change', handleProductChange);
                productSelect.addEventListener('change', handleProductChange);
            }
        });
    }

    // معالج تغيير العناصر
    function handleItemChange(event) {
        window.SalesComponent.calculateItemTotal(event.target);
    }

    // معالج تغيير المنتج
    function handleProductChange(event) {
        window.SalesComponent.updateInvoiceItem(event.target);
        setTimeout(() => {
            window.SalesComponent.calculateInvoiceTotal();
        }, 100);
    }

    // مراقبة إضافة عناصر جديدة
    const originalAddInvoiceItem = window.SalesComponent.addInvoiceItem;
    window.SalesComponent.addInvoiceItem = function() {
        const result = originalAddInvoiceItem.call(this);
        
        // إضافة معالجات الأحداث للعنصر الجديد
        setTimeout(() => {
            addEventListenersToExistingItems();
            this.calculateInvoiceTotal();
        }, 200);
        
        return result;
    };

    // تطبيق المعالجات على العناصر الموجودة
    setTimeout(() => {
        addEventListenersToExistingItems();
    }, 500);

    console.log('✅ تم إصلاح ربط عناصر الفاتورة بالملخص');
}

// إصلاح تحديث ملخص الفاتورة
function fixInvoiceSummaryUpdate() {
    if (typeof window.SalesComponent === 'undefined') return;

    // تحسين وظيفة تحديث عرض الملخص
    window.SalesComponent.updateInvoiceSummaryDisplay = function(subtotal, tax, total, itemCount, currency) {
        const currencySymbol = this.getCurrencySymbol(currency || 'YER');

        console.log(`🔄 تحديث عرض الملخص: ${subtotal.toFixed(2)} + ${tax.toFixed(2)} = ${total.toFixed(2)} ${currencySymbol}`);

        // تحديث عدد العناصر
        const itemCountElement = document.getElementById('invoiceItemCount');
        if (itemCountElement) {
            itemCountElement.textContent = itemCount;
            itemCountElement.className = itemCount > 0 ? 'badge bg-success' : 'badge bg-secondary';
        }

        // تحديث المجموع الفرعي
        const subtotalElement = document.getElementById('invoiceSubtotal');
        if (subtotalElement) {
            subtotalElement.textContent = `${subtotal.toFixed(2)} ${currencySymbol}`;
            this.animateValue(subtotalElement);
        }

        // تحديث الضريبة
        const taxElement = document.getElementById('invoiceTax');
        const taxRow = document.getElementById('taxRow');
        if (taxElement && taxRow) {
            const showTax = this.invoiceSettings?.showTax !== false;
            const taxEnabled = this.invoiceSettings?.taxEnabled !== false;
            
            if (showTax && taxEnabled && tax > 0) {
                taxElement.textContent = `${tax.toFixed(2)} ${currencySymbol}`;
                taxRow.style.display = 'flex';
            } else {
                taxRow.style.display = 'none';
            }
            this.animateValue(taxElement);
        }

        // تحديث الإجمالي
        const totalElement = document.getElementById('invoiceTotal');
        if (totalElement) {
            totalElement.textContent = `${total.toFixed(2)} ${currencySymbol}`;
            this.animateValue(totalElement);

            // تغيير لون الإجمالي حسب القيمة
            if (total > 1000) {
                totalElement.className = 'h5 text-success fw-bold';
            } else if (total > 500) {
                totalElement.className = 'h5 text-primary fw-bold';
            } else {
                totalElement.className = 'h5 text-info fw-bold';
            }
        }

        // تحديث رمز العملة في حقل الخصم
        const discountCurrencySpan = document.querySelector('#discountAmount + .input-group-text');
        if (discountCurrencySpan) {
            discountCurrencySpan.textContent = currencySymbol;
        }

        // تحديث رموز العملة في العناصر
        const currencySymbols = document.querySelectorAll('.item-currency-symbol, .item-total-currency');
        currencySymbols.forEach(symbol => {
            symbol.textContent = currencySymbol;
        });
    };

    console.log('✅ تم إصلاح تحديث ملخص الفاتورة');
}

// إضافة وظيفة فحص الربط
function checkInvoiceLinking() {
    console.log('🔍 فحص ربط عناصر الفاتورة بالملخص...');
    
    const itemsContainer = document.getElementById('invoiceItems');
    if (!itemsContainer) {
        console.warn('❌ عنصر invoiceItems غير موجود');
        return false;
    }

    const items = itemsContainer.children;
    console.log(`📦 عدد العناصر الموجودة: ${items.length}`);

    let validItems = 0;
    Array.from(items).forEach((item, index) => {
        const quantityInput = item.querySelector('.item-quantity');
        const priceInput = item.querySelector('.item-price');
        const totalInput = item.querySelector('.item-total');

        if (quantityInput && priceInput && totalInput) {
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = parseFloat(totalInput.value) || 0;

            if (quantity > 0 && price >= 0) {
                validItems++;
                console.log(`✅ عنصر ${index + 1}: ${quantity} × ${price} = ${total}`);
            } else {
                console.log(`⚠️ عنصر ${index + 1}: قيم غير صحيحة`);
            }
        } else {
            console.log(`❌ عنصر ${index + 1}: عناصر مفقودة`);
        }
    });

    console.log(`📊 العناصر الصحيحة: ${validItems}/${items.length}`);
    
    // فحص ملخص الفاتورة
    const subtotalElement = document.getElementById('invoiceSubtotal');
    const totalElement = document.getElementById('invoiceTotal');
    
    if (subtotalElement && totalElement) {
        console.log(`💰 المجموع الفرعي: ${subtotalElement.textContent}`);
        console.log(`💰 الإجمالي النهائي: ${totalElement.textContent}`);
    } else {
        console.warn('❌ عناصر ملخص الفاتورة غير موجودة');
    }

    return validItems > 0;
}

// تطبيق جميع الإصلاحات
function applyInvoiceItemsSummaryFix() {
    console.log('🚀 تطبيق إصلاحات ربط عناصر الفاتورة بالملخص...');
    
    try {
        fixInvoiceItemsSummaryLinking();
        fixInvoiceSummaryUpdate();
        
        // فحص الربط بعد ثانية واحدة
        setTimeout(() => {
            checkInvoiceLinking();
        }, 1000);
        
        console.log('✅ تم تطبيق جميع إصلاحات ربط عناصر الفاتورة بالملخص بنجاح');
        
        // إشعار المستخدم
        if (typeof window.SalesComponent !== 'undefined' && window.SalesComponent.showToast) {
            window.SalesComponent.showToast('تم إصلاح ربط عناصر الفاتورة بالملخص', 'success');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تطبيق إصلاحات ربط عناصر الفاتورة:', error);
    }
}

// تشغيل الإصلاحات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyInvoiceItemsSummaryFix);
} else {
    applyInvoiceItemsSummaryFix();
}

// تصدير الوظائف للاستخدام الخارجي
window.InvoiceItemsSummaryFix = {
    fixInvoiceItemsSummaryLinking,
    fixInvoiceSummaryUpdate,
    checkInvoiceLinking,
    applyInvoiceItemsSummaryFix
};

console.log('🎉 تم تحميل إصلاحات ربط عناصر الفاتورة بالملخص');
