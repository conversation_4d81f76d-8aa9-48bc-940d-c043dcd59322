{"name": "qimat-alwaed-travel-system", "version": "2.0.0", "description": "نظام إدارة متكامل لوكالات السفر والحج والعمرة - Comprehensive Travel Agency Management System", "main": "index.html", "scripts": {"start": "npx serve . -p 8000", "dev": "npx live-server --port=8000 --entry-file=index.html", "build": "echo 'Building project...' && npm run minify-css && npm run minify-js", "minify-css": "npx clean-css-cli assets/css/*.css -o assets/css/bundle.min.css", "minify-js": "npx terser js/**/*.js -o js/bundle.min.js", "lint": "npx eslint js/**/*.js", "format": "npx prettier --write \"**/*.{js,css,html,md}\"", "test": "echo 'Running tests...' && npm run lint", "backup": "node scripts/backup.js", "deploy": "echo 'Deploying to production...'", "clean": "rm -rf dist/ && rm -f assets/css/bundle.min.css && rm -f js/bundle.min.js"}, "keywords": ["travel-agency", "management-system", "arabic", "rtl", "saudi-arabia", "hajj", "umrah", "booking-system", "accounting", "crm"], "author": {"name": "قمة الوعد للسفريات", "email": "<EMAIL>", "url": "https://www.qimat-alwaed.com"}, "license": "Proprietary", "private": true, "repository": {"type": "git", "url": "https://github.com/qimat-alwaed/travel-system.git"}, "bugs": {"url": "https://github.com/qimat-alwaed/travel-system/issues", "email": "<EMAIL>"}, "homepage": "https://www.qimat-alwaed.com", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"clean-css-cli": "^5.6.2", "eslint": "^8.55.0", "live-server": "^1.2.2", "prettier": "^3.1.0", "serve": "^14.2.1", "terser": "^5.24.0"}, "dependencies": {}, "config": {"port": 8000, "host": "localhost"}, "eslintConfig": {"env": {"browser": true, "es2021": true}, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-unused-vars": "warn", "no-console": "off", "prefer-const": "error", "no-var": "error"}, "globals": {"App": "readonly", "Database": "readonly", "Helpers": "readonly", "Notifications": "readonly"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100, "bracketSpacing": true, "arrowParens": "avoid"}, "directories": {"doc": "docs", "test": "tests"}, "files": ["index.html", "assets/", "js/", "README.md", "LICENSE"]}