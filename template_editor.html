<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر القوالب المتقدم - قيمة الوعد للسفريات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Quill Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: #f8f9fa;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .editor-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .editor-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .editor-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }
        
        .editor-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .editor-toolbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .toolbar-btn {
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 6px;
            color: #495057;
            transition: all 0.2s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .toolbar-btn:hover {
            background: #e9ecef;
            color: #007bff;
        }
        
        .toolbar-btn.active {
            background: #007bff;
            color: white;
        }
        
        .editor-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .editor-sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
        }
        
        .sidebar-section {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .sidebar-title {
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .element-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: grab;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }
        
        .element-item:hover {
            background: #e9ecef;
            border-color: #007bff;
            transform: translateX(-2px);
        }
        
        .element-item:active {
            cursor: grabbing;
        }
        
        .element-icon {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 0.9rem;
            color: white;
        }
        
        .element-text {
            flex: 1;
        }
        
        .element-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        
        .element-desc {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .editor-workspace {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .workspace-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .workspace-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .workspace-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: white;
        }
        
        .workspace-tab:hover:not(.active) {
            color: #495057;
            background: #e9ecef;
        }
        
        .workspace-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }
        
        .design-canvas {
            min-height: 800px;
            background: white;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            padding: 2rem;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .design-canvas.drag-over {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .canvas-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 4rem 2rem;
        }
        
        .canvas-placeholder i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .code-editor {
            background: #2d3748;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            min-height: 400px;
        }
        
        .preview-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }
        
        .properties-panel {
            width: 280px;
            background: white;
            border-left: 1px solid #e9ecef;
            overflow-y: auto;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
        }
        
        .property-group {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .property-title {
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        
        .property-item {
            margin-bottom: 1rem;
        }
        
        .property-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .property-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.2s ease;
        }
        
        .property-input:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .color-picker {
            width: 100%;
            height: 40px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-action {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        
        .variable-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin: 0.25rem;
            cursor: pointer;
            border: 1px solid #bbdefb;
            transition: all 0.2s ease;
        }
        
        .variable-tag:hover {
            background: #bbdefb;
            transform: scale(1.05);
        }
        
        @media (max-width: 1200px) {
            .editor-sidebar {
                width: 250px;
            }
            
            .properties-panel {
                width: 250px;
            }
        }
        
        @media (max-width: 992px) {
            .editor-main {
                flex-direction: column;
            }
            
            .editor-sidebar,
            .properties-panel {
                width: 100%;
                height: 200px;
            }
            
            .workspace-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- رأس المحرر -->
        <div class="editor-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="editor-title">
                        <i class="fas fa-edit me-2"></i>محرر القوالب المتقدم
                    </h1>
                    <p class="editor-subtitle">تصميم وتخصيص القوالب بسهولة ومرونة</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-sm" onclick="saveTemplate()">
                        <i class="fas fa-save me-1"></i>حفظ
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="closeEditor()">
                        <i class="fas fa-times me-1"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>
        
        <!-- شريط الأدوات -->
        <div class="editor-toolbar">
            <div class="toolbar-group">
                <button class="toolbar-btn" title="تراجع">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="toolbar-btn" title="إعادة">
                    <i class="fas fa-redo"></i>
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="toolbar-btn" title="نسخ">
                    <i class="fas fa-copy"></i>
                </button>
                <button class="toolbar-btn" title="لصق">
                    <i class="fas fa-paste"></i>
                </button>
                <button class="toolbar-btn" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="toolbar-btn" title="تكبير">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="toolbar-btn" title="تصغير">
                    <i class="fas fa-search-minus"></i>
                </button>
                <button class="toolbar-btn" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
            
            <div class="toolbar-group">
                <select class="form-select form-select-sm" style="width: auto;">
                    <option>A4</option>
                    <option>Letter</option>
                    <option>Legal</option>
                </select>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="editor-main">
            <!-- الشريط الجانبي للعناصر -->
            <div class="editor-sidebar">
                <div class="sidebar-section">
                    <h6 class="sidebar-title">العناصر الأساسية</h6>
                    
                    <div class="element-item" draggable="true" data-element="text">
                        <div class="element-icon" style="background: #007bff;">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="element-text">
                            <div class="element-name">نص</div>
                            <div class="element-desc">إضافة نص عادي</div>
                        </div>
                    </div>
                    
                    <div class="element-item" draggable="true" data-element="heading">
                        <div class="element-icon" style="background: #28a745;">
                            <i class="fas fa-heading"></i>
                        </div>
                        <div class="element-text">
                            <div class="element-name">عنوان</div>
                            <div class="element-desc">عنوان رئيسي أو فرعي</div>
                        </div>
                    </div>
                    
                    <div class="element-item" draggable="true" data-element="table">
                        <div class="element-icon" style="background: #ffc107;">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="element-text">
                            <div class="element-name">جدول</div>
                            <div class="element-desc">جدول بيانات</div>
                        </div>
                    </div>
                    
                    <div class="element-item" draggable="true" data-element="image">
                        <div class="element-icon" style="background: #17a2b8;">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="element-text">
                            <div class="element-name">صورة</div>
                            <div class="element-desc">إدراج صورة</div>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h6 class="sidebar-title">المتغيرات</h6>
                    
                    <div class="variable-tag" onclick="insertVariable('{{company.name}}')">
                        اسم الشركة
                    </div>
                    <div class="variable-tag" onclick="insertVariable('{{invoice.number}}')">
                        رقم الفاتورة
                    </div>
                    <div class="variable-tag" onclick="insertVariable('{{customer.name}}')">
                        اسم العميل
                    </div>
                    <div class="variable-tag" onclick="insertVariable('{{invoice.date}}')">
                        تاريخ الفاتورة
                    </div>
                    <div class="variable-tag" onclick="insertVariable('{{invoice.total}}')">
                        إجمالي الفاتورة
                    </div>
                </div>
            </div>
            
            <!-- منطقة العمل -->
            <div class="editor-workspace">
                <div class="workspace-tabs">
                    <button class="workspace-tab active" data-tab="design">
                        <i class="fas fa-paint-brush me-1"></i>التصميم
                    </button>
                    <button class="workspace-tab" data-tab="code">
                        <i class="fas fa-code me-1"></i>الكود
                    </button>
                    <button class="workspace-tab" data-tab="preview">
                        <i class="fas fa-eye me-1"></i>المعاينة
                    </button>
                </div>
                
                <div class="workspace-content">
                    <div id="design-tab" class="tab-content">
                        <div class="design-canvas" id="designCanvas">
                            <div class="canvas-placeholder">
                                <i class="fas fa-mouse-pointer"></i>
                                <h5>اسحب العناصر هنا لبدء التصميم</h5>
                                <p>يمكنك سحب العناصر من الشريط الجانبي وإفلاتها هنا</p>
                            </div>
                        </div>
                    </div>
                    
                    <div id="code-tab" class="tab-content" style="display: none;">
                        <div class="code-editor" id="codeEditor">
                            <!-- محرر الكود سيتم تحميله هنا -->
                        </div>
                    </div>
                    
                    <div id="preview-tab" class="tab-content" style="display: none;">
                        <iframe class="preview-frame" id="previewFrame"></iframe>
                    </div>
                </div>
            </div>
            
            <!-- لوحة الخصائص -->
            <div class="properties-panel">
                <div class="property-group">
                    <h6 class="property-title">خصائص العنصر</h6>
                    <div id="elementProperties">
                        <p class="text-muted text-center">اختر عنصراً لعرض خصائصه</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="btn-action btn-primary" onclick="saveTemplate()">
                <i class="fas fa-save me-1"></i>حفظ القالب
            </button>
            <button class="btn-action btn-success" onclick="previewTemplate()">
                <i class="fas fa-eye me-1"></i>معاينة
            </button>
            <button class="btn-action btn-secondary" onclick="exportTemplate()">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/template-editor.js"></script>
</body>
</html>
