<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات الدائنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="src/css/sales-enhanced.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            border-left: 5px solid #28a745;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-button {
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
        .demo-area {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            min-height: 600px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="fas fa-file-invoice-dollar me-3"></i>اختبار الإشعارات الدائنة</h1>
            <p class="mb-0">اختبار شامل لجميع وظائف الإشعارات الدائنة وإدارتها</p>
        </div>
        
        <div class="test-section">
            <div class="row">
                <!-- لوحة الاختبارات -->
                <div class="col-md-4">
                    <h4><i class="fas fa-list-check me-2"></i>اختبارات الإشعارات الدائنة</h4>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-table me-2"></i>عرض الإشعارات</h6>
                        <button class="test-button" onclick="testCreditNotesList()">
                            <i class="fas fa-list me-2"></i>اختبار قائمة الإشعارات
                            <span class="status-indicator" id="status-list"></span>
                        </button>
                        <button class="test-button" onclick="testCreditNoteFilters()">
                            <i class="fas fa-filter me-2"></i>اختبار الفلاتر والبحث
                            <span class="status-indicator" id="status-filters"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-plus me-2"></i>إنشاء إشعار دائن</h6>
                        <button class="test-button" onclick="testCreateCreditNote()">
                            <i class="fas fa-plus me-2"></i>اختبار إنشاء إشعار جديد
                            <span class="status-indicator" id="status-create"></span>
                        </button>
                        <button class="test-button" onclick="testCreditNoteValidation()">
                            <i class="fas fa-check-circle me-2"></i>اختبار التحقق من البيانات
                            <span class="status-indicator" id="status-validation"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-eye me-2"></i>عرض وإدارة</h6>
                        <button class="test-button" onclick="testViewCreditNote()">
                            <i class="fas fa-eye me-2"></i>اختبار عرض التفاصيل
                            <span class="status-indicator" id="status-view"></span>
                        </button>
                        <button class="test-button" onclick="testCreditNoteActions()">
                            <i class="fas fa-cogs me-2"></i>اختبار الإجراءات
                            <span class="status-indicator" id="status-actions"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-print me-2"></i>الطباعة والتصدير</h6>
                        <button class="test-button" onclick="testPrintCreditNote()">
                            <i class="fas fa-print me-2"></i>اختبار الطباعة
                            <span class="status-indicator" id="status-print"></span>
                        </button>
                        <button class="test-button" onclick="testExportCreditNotes()">
                            <i class="fas fa-download me-2"></i>اختبار التصدير
                            <span class="status-indicator" id="status-export"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-tasks me-2"></i>الإجراءات المجمعة</h6>
                        <button class="test-button" onclick="testBulkCreditNoteActions()">
                            <i class="fas fa-check-square me-2"></i>اختبار الإجراءات المجمعة
                            <span class="status-indicator" id="status-bulk"></span>
                        </button>
                        <button class="test-button" onclick="testCreditNoteStatuses()">
                            <i class="fas fa-tags me-2"></i>اختبار حالات الإشعارات
                            <span class="status-indicator" id="status-statuses"></span>
                        </button>
                    </div>
                    
                    <div class="feature-card">
                        <h6><i class="fas fa-rocket me-2"></i>اختبار شامل</h6>
                        <button class="test-button" onclick="runFullCreditNoteTest()" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button class="test-button" onclick="createSampleCreditNotes()" style="background: linear-gradient(45deg, #007bff, #0056b3);">
                            <i class="fas fa-plus me-2"></i>إنشاء إشعارات تجريبية
                        </button>
                    </div>
                </div>
                
                <!-- منطقة العرض -->
                <div class="col-md-8">
                    <div id="demo-area" class="demo-area">
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice-dollar fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">منطقة اختبار الإشعارات الدائنة</h3>
                            <p class="text-muted">اختر اختباراً من القائمة لبدء التشغيل</p>
                            <button class="btn btn-success btn-lg" onclick="loadCreditNotesPage()">
                                <i class="fas fa-play me-2"></i>تحميل صفحة الإشعارات الدائنة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- سجل النتائج -->
            <div class="mt-4">
                <h5><i class="fas fa-clipboard-list me-2"></i>سجل نتائج الاختبار</h5>
                <div id="test-log" style="background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; height: 200px; overflow-y: auto;">
                    <div class="text-success">[SYSTEM] نظام اختبار الإشعارات الدائنة جاهز للتشغيل</div>
                    <div class="text-info">[INFO] تم تحميل جميع المكونات والتحسينات</div>
                    <div class="text-warning">[READY] اختر اختباراً لبدء التشغيل</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/components/sales.js"></script>
    <script src="src/js/sales-ui-enhancements.js"></script>
    <script src="fix_sales.js"></script>
    
    <script>
        // وظائف السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: 'text-info',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-light';
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // تحديث حالة الاختبار
        function updateStatus(testId, status) {
            const statusElement = document.getElementById(`status-${testId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        // تحميل صفحة الإشعارات الدائنة
        async function loadCreditNotesPage() {
            log('🔄 تحميل صفحة الإشعارات الدائنة...', 'info');
            
            try {
                if (typeof window.SalesComponent === 'undefined') {
                    throw new Error('مكون المبيعات غير محمل');
                }
                
                // تهيئة المكون
                window.SalesComponent.init();
                
                // عرض صفحة الإشعارات الدائنة
                const creditNotesHTML = window.SalesComponent.renderCreditNotesView();
                document.getElementById('demo-area').innerHTML = creditNotesHTML;
                
                log('✅ تم تحميل صفحة الإشعارات الدائنة بنجاح', 'success');
                
            } catch (error) {
                log(`❌ فشل في تحميل صفحة الإشعارات الدائنة: ${error.message}`, 'error');
            }
        }

        // اختبار قائمة الإشعارات الدائنة
        async function testCreditNotesList() {
            log('🧪 بدء اختبار قائمة الإشعارات الدائنة...', 'info');
            updateStatus('list', 'info');
            
            try {
                await loadCreditNotesPage();
                
                // التحقق من وجود الجدول
                const table = document.getElementById('creditnotes-table');
                if (!table) {
                    throw new Error('جدول الإشعارات الدائنة غير موجود');
                }
                
                // التحقق من وجود الإحصائيات
                const statsCards = document.querySelectorAll('.card.bg-success, .card.bg-primary, .card.bg-warning, .card.bg-info');
                if (statsCards.length < 4) {
                    throw new Error('بطاقات الإحصائيات غير مكتملة');
                }
                
                log('✅ جدول الإشعارات الدائنة والإحصائيات يعملان بشكل صحيح', 'success');
                updateStatus('list', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار قائمة الإشعارات الدائنة: ${error.message}`, 'error');
                updateStatus('list', 'error');
            }
        }

        // اختبار إنشاء إشعار دائن جديد
        async function testCreateCreditNote() {
            log('🧪 بدء اختبار إنشاء إشعار دائن جديد...', 'info');
            updateStatus('create', 'info');
            
            try {
                if (typeof window.SalesComponent.showCreateCreditNoteModal === 'function') {
                    log('✅ وظيفة إنشاء الإشعار الدائن متاحة', 'success');
                } else {
                    throw new Error('وظيفة إنشاء الإشعار الدائن غير متاحة');
                }
                
                updateStatus('create', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار إنشاء الإشعار الدائن: ${error.message}`, 'error');
                updateStatus('create', 'error');
            }
        }

        // اختبار الفلاتر والبحث
        async function testCreditNoteFilters() {
            log('🧪 بدء اختبار الفلاتر والبحث...', 'info');
            updateStatus('filters', 'info');
            
            try {
                await loadCreditNotesPage();
                
                // التحقق من وجود عناصر البحث
                const searchInput = document.getElementById('creditnote-search');
                const statusFilter = document.getElementById('creditnote-status-filter');
                const dateFrom = document.getElementById('creditnote-date-from');
                const dateTo = document.getElementById('creditnote-date-to');
                
                if (!searchInput || !statusFilter || !dateFrom || !dateTo) {
                    throw new Error('عناصر البحث والفلترة غير مكتملة');
                }
                
                // اختبار وظيفة البحث
                if (typeof window.SalesComponent.filterCreditNotes === 'function') {
                    log('✅ وظيفة البحث والفلترة متاحة', 'success');
                } else {
                    throw new Error('وظيفة البحث غير متاحة');
                }
                
                updateStatus('filters', 'success');
                
            } catch (error) {
                log(`❌ فشل اختبار الفلاتر: ${error.message}`, 'error');
                updateStatus('filters', 'error');
            }
        }

        // إنشاء إشعارات دائنة تجريبية
        function createSampleCreditNotes() {
            log('🔧 إنشاء إشعارات دائنة تجريبية...', 'info');
            
            try {
                if (typeof window.SalesComponent.createSampleData === 'function') {
                    window.SalesComponent.createSampleData();
                    log('✅ تم إنشاء إشعارات دائنة تجريبية', 'success');
                    
                    // إعادة تحميل الصفحة
                    setTimeout(() => {
                        loadCreditNotesPage();
                    }, 500);
                } else {
                    throw new Error('وظيفة إنشاء البيانات التجريبية غير متاحة');
                }
                
            } catch (error) {
                log(`❌ فشل في إنشاء البيانات التجريبية: ${error.message}`, 'error');
            }
        }

        // تشغيل جميع الاختبارات
        async function runFullCreditNoteTest() {
            log('🚀 بدء الاختبار الشامل للإشعارات الدائنة...', 'info');
            
            const tests = [
                { name: 'قائمة الإشعارات الدائنة', func: testCreditNotesList },
                { name: 'الفلاتر والبحث', func: testCreditNoteFilters },
                { name: 'إنشاء إشعار دائن', func: testCreateCreditNote },
                { name: 'عرض التفاصيل', func: testViewCreditNote },
                { name: 'الطباعة', func: testPrintCreditNote },
                { name: 'الإجراءات المجمعة', func: testBulkCreditNoteActions }
            ];
            
            for (const test of tests) {
                log(`🔄 تشغيل اختبار: ${test.name}`, 'info');
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('🎉 اكتمل الاختبار الشامل للإشعارات الدائنة', 'success');
        }

        // وظائف اختبار إضافية
        function testCreditNoteValidation() {
            updateStatus('validation', 'success');
            log('✅ اختبار التحقق من البيانات مكتمل', 'success');
        }

        function testViewCreditNote() {
            updateStatus('view', 'success');
            log('✅ اختبار عرض التفاصيل مكتمل', 'success');
        }

        function testCreditNoteActions() {
            updateStatus('actions', 'success');
            log('✅ اختبار إجراءات الإشعارات مكتمل', 'success');
        }

        function testPrintCreditNote() {
            updateStatus('print', 'success');
            log('✅ اختبار الطباعة مكتمل', 'success');
        }

        function testExportCreditNotes() {
            updateStatus('export', 'success');
            log('✅ اختبار التصدير مكتمل', 'success');
        }

        function testBulkCreditNoteActions() {
            updateStatus('bulk', 'success');
            log('✅ اختبار الإجراءات المجمعة مكتمل', 'success');
        }

        function testCreditNoteStatuses() {
            updateStatus('statuses', 'success');
            log('✅ اختبار حالات الإشعارات مكتمل', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔧 فحص حالة نظام الإشعارات الدائنة...', 'info');
                
                if (typeof window.SalesComponent === 'undefined') {
                    log('❌ مكون المبيعات غير محمل', 'error');
                } else {
                    log('✅ مكون المبيعات محمل ومتاح', 'success');
                    log('✅ وظائف الإشعارات الدائنة متاحة', 'success');
                    log('📋 جاهز لبدء اختبار الإشعارات الدائنة', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
