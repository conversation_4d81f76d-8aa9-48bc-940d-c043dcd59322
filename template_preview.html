<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة وطباعة القوالب - قيمة الوعد للسفريات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .preview-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .preview-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }
        
        .preview-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .preview-toolbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .zoom-btn {
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 6px;
            color: #495057;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .zoom-btn:hover {
            background: #e9ecef;
            color: #007bff;
        }
        
        .zoom-level {
            font-weight: 600;
            color: #495057;
            min-width: 60px;
            text-align: center;
        }
        
        .preview-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .preview-sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #e9ecef;
            overflow-y: auto;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
        }
        
        .sidebar-section {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .sidebar-title {
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .preview-workspace {
            flex: 1;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            overflow: auto;
        }
        
        .preview-paper {
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .preview-paper.a4 {
            width: 210mm;
            min-height: 297mm;
        }
        
        .preview-paper.letter {
            width: 8.5in;
            min-height: 11in;
        }
        
        .preview-content {
            padding: 2rem;
            min-height: 100%;
        }
        
        .data-form {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-control {
            font-size: 0.9rem;
            border-radius: 6px;
        }
        
        .btn-action {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.25rem;
            width: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .print-options {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .option-group {
            margin-bottom: 1rem;
        }
        
        .option-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .page-size-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }
        
        .size-option {
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .size-option:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .size-option.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }
        
        .orientation-selector {
            display: flex;
            gap: 0.5rem;
        }
        
        .orientation-option {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        
        .orientation-option:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .orientation-option.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }
        
        .variable-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .variable-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid #f8f9fa;
            font-size: 0.9rem;
        }
        
        .variable-item:last-child {
            border-bottom: none;
        }
        
        .variable-name {
            font-weight: 600;
            color: #495057;
        }
        
        .variable-value {
            color: #6c757d;
            font-size: 0.8rem;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .loading-spinner {
            text-align: center;
        }
        
        .spinner-border {
            color: #007bff;
        }
        
        @media print {
            body * {
                visibility: hidden;
            }
            
            .preview-paper,
            .preview-paper * {
                visibility: visible;
            }
            
            .preview-paper {
                position: absolute;
                left: 0;
                top: 0;
                width: 100% !important;
                height: 100% !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                margin: 0 !important;
            }
        }
        
        @media (max-width: 1200px) {
            .preview-sidebar {
                width: 250px;
            }
        }
        
        @media (max-width: 992px) {
            .preview-main {
                flex-direction: column;
            }
            
            .preview-sidebar {
                width: 100%;
                height: 300px;
                border-left: none;
                border-top: 1px solid #e9ecef;
            }
            
            .preview-workspace {
                padding: 1rem;
            }
            
            .preview-paper {
                transform: scale(0.7);
            }
        }
        
        @media (max-width: 768px) {
            .preview-toolbar {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }
            
            .toolbar-left,
            .toolbar-right {
                width: 100%;
                justify-content: center;
            }
            
            .preview-paper {
                transform: scale(0.5);
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- رأس المعاينة -->
        <div class="preview-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="preview-title">
                        <i class="fas fa-eye me-2"></i>معاينة وطباعة القوالب
                    </h1>
                    <p class="preview-subtitle">معاينة القالب قبل الطباعة أو التصدير</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-light btn-sm" onclick="refreshPreview()">
                        <i class="fas fa-sync me-1"></i>تحديث
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="window.close()">
                        <i class="fas fa-times me-1"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>
        
        <!-- شريط الأدوات -->
        <div class="preview-toolbar">
            <div class="toolbar-left">
                <div class="zoom-controls">
                    <button class="zoom-btn" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level" id="zoomLevel">100%</span>
                    <button class="zoom-btn" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                </div>
                
                <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                    <option value="a4">A4</option>
                    <option value="letter">Letter</option>
                    <option value="legal">Legal</option>
                </select>
            </div>
            
            <div class="toolbar-right">
                <button class="btn btn-primary btn-sm" onclick="printTemplate()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
                <button class="btn btn-success btn-sm" onclick="exportPDF()">
                    <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                </button>
                <button class="btn btn-info btn-sm" onclick="exportImage()">
                    <i class="fas fa-image me-1"></i>تصدير صورة
                </button>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="preview-main">
            <!-- منطقة المعاينة -->
            <div class="preview-workspace">
                <div class="preview-paper a4" id="previewPaper">
                    <div class="preview-content" id="previewContent">
                        <!-- محتوى القالب سيتم تحميله هنا -->
                        <div class="text-center p-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">جاري تحميل القالب...</h5>
                        </div>
                    </div>
                    
                    <!-- طبقة التحميل -->
                    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                        <div class="loading-spinner">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري معالجة القالب...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الشريط الجانبي -->
            <div class="preview-sidebar">
                <!-- بيانات القالب -->
                <div class="sidebar-section">
                    <h6 class="sidebar-title">بيانات القالب</h6>
                    
                    <div class="data-form">
                        <div class="form-group">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" value="قيمة الوعد للسفريات" onchange="updateVariable('company.name', this.value)">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">رقم الفاتورة</label>
                            <input type="text" class="form-control" value="INV-2024-001" onchange="updateVariable('invoice.number', this.value)">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" value="أحمد محمد السعيد" onchange="updateVariable('customer.name', this.value)">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">تاريخ الفاتورة</label>
                            <input type="date" class="form-control" value="2024-03-15" onchange="updateVariable('invoice.date', this.value)">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">إجمالي المبلغ</label>
                            <input type="number" class="form-control" value="2760" onchange="updateVariable('invoice.total', this.value)">
                        </div>
                    </div>
                    
                    <button class="btn-action btn-primary" onclick="applyData()">
                        <i class="fas fa-sync me-1"></i>تطبيق البيانات
                    </button>
                </div>
                
                <!-- خيارات الطباعة -->
                <div class="sidebar-section">
                    <h6 class="sidebar-title">خيارات الطباعة</h6>
                    
                    <div class="print-options">
                        <div class="option-group">
                            <div class="option-label">حجم الورقة</div>
                            <div class="page-size-selector">
                                <div class="size-option active" data-size="a4">A4</div>
                                <div class="size-option" data-size="letter">Letter</div>
                            </div>
                        </div>
                        
                        <div class="option-group">
                            <div class="option-label">اتجاه الورقة</div>
                            <div class="orientation-selector">
                                <div class="orientation-option active" data-orientation="portrait">
                                    <i class="fas fa-file-alt"></i><br>عمودي
                                </div>
                                <div class="orientation-option" data-orientation="landscape">
                                    <i class="fas fa-file-alt fa-rotate-90"></i><br>أفقي
                                </div>
                            </div>
                        </div>
                        
                        <div class="option-group">
                            <div class="option-label">جودة الطباعة</div>
                            <select class="form-select form-select-sm">
                                <option>عالية (300 DPI)</option>
                                <option selected>متوسطة (150 DPI)</option>
                                <option>منخفضة (72 DPI)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- المتغيرات المتاحة -->
                <div class="sidebar-section">
                    <h6 class="sidebar-title">المتغيرات المتاحة</h6>
                    
                    <div class="variable-list">
                        <div class="variable-item">
                            <span class="variable-name">{{company.name}}</span>
                            <span class="variable-value">اسم الشركة</span>
                        </div>
                        <div class="variable-item">
                            <span class="variable-name">{{invoice.number}}</span>
                            <span class="variable-value">رقم الفاتورة</span>
                        </div>
                        <div class="variable-item">
                            <span class="variable-name">{{customer.name}}</span>
                            <span class="variable-value">اسم العميل</span>
                        </div>
                        <div class="variable-item">
                            <span class="variable-name">{{invoice.date}}</span>
                            <span class="variable-value">تاريخ الفاتورة</span>
                        </div>
                        <div class="variable-item">
                            <span class="variable-name">{{invoice.total}}</span>
                            <span class="variable-value">إجمالي المبلغ</span>
                        </div>
                    </div>
                </div>
                
                <!-- إجراءات سريعة -->
                <div class="sidebar-section">
                    <h6 class="sidebar-title">إجراءات سريعة</h6>
                    
                    <button class="btn-action btn-success" onclick="saveAsPDF()">
                        <i class="fas fa-save me-1"></i>حفظ كـ PDF
                    </button>
                    
                    <button class="btn-action btn-info" onclick="emailTemplate()">
                        <i class="fas fa-envelope me-1"></i>إرسال بالبريد
                    </button>
                    
                    <button class="btn-action btn-secondary" onclick="shareTemplate()">
                        <i class="fas fa-share me-1"></i>مشاركة
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/template-preview.js"></script>
</body>
</html>
